version: '3.7'
services:
  app:
    &app
    build:
      context: .
      dockerfile: ./docker/app/Dockerfile
    image: ecb-backend:1.0.0
    tmpfs:
      - /tmp

  backend:
    &backend
    <<: *app
    stdin_open: true
    tty: true
    volumes:
      - .:/usr/src/app:cached
      - ~/.ssh:/root/.ssh:ro
      - rails_cache:/usr/src/app/tmp/cache
      - bundle:/usr/local/bundle
      - node_modules:/usr/src/app/node_modules
      - packs:/usr/src/app/public/packs
    environment:
      - NODE_ENV=development
      - RAILS_ENV=${RAILS_ENV:-development}
      - REDIS_URL=redis://redis:6379/
      - BOOTSNAP_CACHE_DIR=/bundle/bootsnap
      - WEB_CONCURRENCY=1
      - HISTFILE=/usr/src/app/log/.bash_history
      - EDITOR=vi
    depends_on:
      # - redis
      - mysql_ecb
    networks:
      - default

  runner:
    <<: *backend
    command: /bin/bash
    ports:
      - "3004:3000"
  rails:
    <<: *backend
    command: bash -c "rm -f tmp/pids/server.pid && bin/rails s -b '0.0.0.0'"
    ports:
      - "3000:3000"

  sidekiq:
    <<: *backend
    command: bundle exec sidekiq -C config/sidekiq.yml
    depends_on:
      - redis

  mysql_ecb:
    build:
      context: ./docker/mysql
    command:
      - --character-set-server=utf8
      - --collation-server=utf8_general_ci
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: ecb_development
    ports:
      - "3307:3308"
    volumes:
      - mysql_ecb:/var/lib/mysql-ecb/data:delegated
      - ./log:/root/log:cached
    networks:
      - default

  redis:
    image: redis:7.0
    volumes:
      - redis:/data
    ports:
      - 6379
    networks:
      - default

volumes:
  mysql_ecb:
  redis:
  bundle:
  node_modules:
  rails_cache:
  packs:


networks:
  default:
    name: ecb-network
