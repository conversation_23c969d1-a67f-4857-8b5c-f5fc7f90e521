source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.2.2'

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem 'rails', '~> 7.0.4', '>= *******'

gem 'devise'
gem 'devise_token_auth', '~> 1.2', '>= 1.2.5'
gem 'devise_uid'

gem 'aws-sdk-s3'
gem 'image_processing', '~> 1.2'

gem 'rubyzip'

# Use mysql as the database for Active Record
gem 'mysql2', '~> 0.5.5'

# Use the Puma web server [https://github.com/puma/puma]
gem 'puma', '~> 6.3'

# Sidekiq. See: https://github.com/mperham/sidekiq
gem 'sidekiq', '~> 6.5'
gem 'sidekiq-cron'

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem 'jbuilder'

gem 'kaminari'

gem 'roo'

gem 'fcm'
gem 'fcmpush'
gem 'rpush'

gem 'faraday'

gem 'redis'

gem 'redis-client'

gem 'caxlsx'
gem 'caxlsx_rails'

gem 'barnes'

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
gem 'rack-cors'

gem 'rails_admin', '~> 3.1', '>= 3.1.1'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'debug', platforms: %i[mri mingw x64_mingw]
end

group :development do
  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
  gem 'rubocop-rails'
  gem 'solargraph'
end
gem 'sassc-rails'

gem 'stackprof'
gem 'sentry-ruby'
gem 'sentry-rails'
gem 'sendgrid-ruby'
