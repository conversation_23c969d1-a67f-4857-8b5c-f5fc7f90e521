s3 = Aws::S3::Resource.new(
  region: 'ap-south-1',
  access_key_id: Rails.application.credentials[Rails.env.to_sym].dig(:aws, :access_key_id),
  secret_access_key: Rails.application.credentials[Rails.env.to_sym].dig(:aws, :secret_access_key)
)

AWS_S3_BUCKET = s3.bucket(Rails.application.credentials[Rails.env.to_sym].dig(:aws, :bucket))
AWS_S3_DOWNLOAD_BUCKET = s3.bucket(Rails.application.credentials[Rails.env.to_sym].dig(:aws, :download_bucket))
