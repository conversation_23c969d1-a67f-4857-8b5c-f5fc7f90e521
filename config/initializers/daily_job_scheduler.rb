# if defined?(Rails) && Rails.env.production?
#   Rails.application.config.after_initialize do
#     time_now = Time.zone.now
#     start_of_day = time_now.hour > 3 && time_now.min > 30 ? Time.zone.now.beginning_of_day.tomorrow : Time.zone.now.beginning_of_day
#     time_to_run = start_of_day.change(hour: 3, min: 30)

#     interval = 24.hours

#     while true
#       NextFollowUpNotifyJob.set(wait_until: time_to_run).perform_later

#       time_to_run += interval

#       if time_to_run < time_now
#         time_to_run = time_now + interval
#       end

#       sleep(time_to_run - time_now)
#     end
#   end
# end
