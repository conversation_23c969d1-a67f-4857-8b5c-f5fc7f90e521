sendbird_chat_users_remove:
  cron: "5 0 * * *"
  class: "ChatUsersRemovalJob"
  queue: default
# reports_generate:
#   cron: "0 0 * * *"
#   class: "ReportsGenerateJob"
#   queue: default
database_report_generate:
  cron: "0 0 * * 7"
  class: "DatabaseReportGenerateJob"
  queue: default
compaint_database_report_generate:
  cron: "0 1 * * 7"
  class: "ComplaintDatabaseReportGenerateJob"
  queue: default
installations_report_generate:
  cron: "0 2 * * 7"
  class: "InstallationsReportGenerateJob"
  queue: default
invoicing_installation_report_generate:
  cron: "0 3 * * 7"
  class: "InvoicingInstallationReportGenerateJob"
  queue: default
invoicing_survey_report_generate:
  cron: "0 4 * * 7"
  class: "InvoicingSurveyReportGenerateJob"
  queue: default
payment_tracker_report_generate:
  cron: "0 5 * * 7"
  class: "PaymentTrackerReportGenerateJob"
  queue: default
