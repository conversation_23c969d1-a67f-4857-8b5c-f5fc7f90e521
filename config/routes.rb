require 'sidekiq/web'

Rails.application.routes.draw do
  mount RailsAdmin::Engine => '/mgmt', as: 'rails_admin'
  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    username == Rails.application.credentials[Rails.env.to_sym].dig(:sidekiq,
                                                                    :username) && password == Rails.application.credentials[Rails.env.to_sym].dig(
                                                                      :sidekiq, :password
                                                                    )
  end
  mount Sidekiq::Web => '/sidekiq'

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  # root "articles#index"

  namespace 'api', defaults: { format: :json } do
    namespace 'v1' do
      mount_devise_token_auth_for 'User', at: 'auth', controllers: {
        sessions: 'api/v1/sessions'
      }
      resources :users, only: %i[index show create update] do
        get :me, on: :collection
        post :save_fcm_data, on: :collection
        post :transfer_bookings, on: :member
      end
      post 'users/:id/roles', to: 'users#create_role'
      post 'users/:id/create_roles', to: 'users#create_roles'
      put 'users/:id/roles/:role_id', to: 'users#update_role'
      delete 'users/:id/roles/:role_id', to: 'users#remove_role'

      resources :brands, only: %i[index show create update]
      post 'brands/:id/models', to: 'brands#create_model'
      put 'brands/:id/models/:model_id', to: 'brands#update_model'
      post 'brands/:id/charger_types', to: 'brands#create_charger_type'
      put 'brands/:id/charger_types/:charger_type_id', to: 'brands#update_charger_type'

      resources :brand_models, only: %i[show]
      post 'brand_models/:id/pricings', to: 'brand_models#create_pricing'
      put 'brand_models/:id/pricings/:pricing_id', to: 'brand_models#update_pricing'
      delete 'brand_models/:id/pricings/:pricing_id', to: 'brand_models#delete_pricing'

      resources :dealers, only: %i[index show create update]

      resources :partners, only: %i[index show create update]
      post 'partners/:id/locations', to: 'partners#create_location'
      post 'partners/:id/create_locations', to: 'partners#create_locations'
      delete 'partners/:id/locations/:location_id', to: 'partners#remove_location'

      post 'assignments/:id/payments', to: 'assignments#create_payment'
      post 'assignments/:id/refunds', to: 'assignments#create_refund'
      put 'assignments/:id/payments', to: 'assignments#edit_payment'
      delete 'assignments/:id/payments/:paymentId', to: 'assignments#delete_payment'
      delete 'assignments/:id/refunds/:refundId', to: 'assignments#delete_refund'
      put 'assignments/:id/refunds', to: 'assignments#edit_refund'

      resources :locations, only: %i[index show create update]
      resources :form_sections, only: %i[index show create update]
      resources :form_questions, only: %i[index show create update]

      resources :bookings, only: %i[index show create update] do
        post :import, on: :collection
        post :import_booking_status, on: :collection
        post :import_invoices, on: :collection
        post :transfer_bookings, on: :collection
        post :import_call_logs, on: :collection
        put :cancel, on: :member
        put :on_hold, on: :member
        post :add_customer_connect_log, on: :member
        get :customer_connect_logs, on: :member
        put :edit_customer_connect_log, on: :member
        post :import_payment_details, on: :collection
      end
      resources :complaints, only: %i[index show create update] do
        post :import, on: :collection
        put :cancel, on: :member
        put :close, on: :member
        post :add_customer_connect_log, on: :member
        get :customer_connect_logs, on: :member
        put :edit_customer_connect_log, on: :member
      end
      resources :surveys, only: %i[index show create update] do
        put :cancel, on: :member
        put :on_hold, on: :member
        post :add_customer_connect_log, on: :member
        post :trigger_email, on: :member
        post :generate_payment_link, on: :member
        get :customer_connect_logs, on: :member
        get :payments, on: :member
        get :refunds, on: :member
        put :edit_customer_connect_log, on: :member
      end
      resources :visits, only: %i[index show create update] do
        put :cancel, on: :member
        post :add_customer_connect_log, on: :member
        get :customer_connect_logs, on: :member
        put :edit_customer_connect_log, on: :member
      end
      resources :installations, only: %i[index show create update] do
        put :cancel, on: :member
        put :on_hold, on: :member
        post :add_customer_connect_log, on: :member
        get :customer_connect_logs, on: :member
        get :payments, on: :member
        get :refunds, on: :member
        put :edit_customer_connect_log, on: :member
      end
      resources :assignments, only: %i[index show update] do
        patch :cancel, on: :member
        patch :on_hold, on: :member
        post :add_customer_connect_log, on: :member
        get :customer_connect_logs, on: :member
        patch :start_work, on: :member
        patch :end_work, on: :member
        patch :add_notes, on: :member
        patch :customer_update, on: :member
        get :form_sections, on: :member
        get :form_questions, on: :member
        post :response_submit, on: :member
        post :add_attachment, on: :member
        post :remove_attachment, on: :member
        patch :approve, on: :member
        patch :send_for_approval, on: :member
        get :download_zip, on: :member
      end

      get 'reports/installations', to: 'reports#installations'
      get 'reports/database', to: 'reports#database'
      get 'reports/database_two', to: 'reports#database_two'
      get 'reports/database_three', to: 'reports#database_three'
      get 'reports/complaints_database', to: 'reports#complaints_database'
      get 'reports/database_recent', to: 'reports#database_recent'
      get 'reports/database_recent_new', to: 'reports#database_recent_new'
      get 'reports/invoicing_survey', to: 'reports#invoicing_survey'
      get 'reports/invoicing_installation', to: 'reports#invoicing_installation'
      get 'reports/payment_tracker', to: 'reports#new_payment_tracker'
      get 'reports/payment_tracker_recent', to: 'reports#payment_tracker_recent'
      get 'reports/client_database', to: 'reports#client_database'
      get 'reports/client_complaints_database', to: 'reports#client_complaints_database'
      post 'reports/full_report', to: 'reports#full_report'
      post 'reports/refresh_report', to: 'reports#refresh_report'
      post 'reports/email_report_request', to: 'reports#email_report_request'
      get 'dropdowns', to: 'dropdowns#index'

      # Setu webhook endpoints
      post 'setu/webhooks/payment_notification', to: 'setu_webhooks#payment_notification'
    end
  end
end
