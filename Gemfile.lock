GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.5)
      public_suffix (>= 2.0.2, < 6.0)
    ast (2.4.2)
    aws-eventstream (1.2.0)
    aws-partitions (1.757.0)
    aws-sdk-core (3.171.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.5)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.63.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.121.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.5.2)
      aws-eventstream (~> 1, >= 1.0.2)
    backport (1.2.0)
    barnes (0.0.9)
      multi_json (~> 1)
      statsd-ruby (~> 1.1)
    bcrypt (3.1.18)
    benchmark (0.2.1)
    bigdecimal (3.1.8)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    caxlsx (3.4.1)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.3)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    concurrent-ruby (1.2.2)
    connection_pool (2.4.0)
    crass (1.0.6)
    date (3.3.3)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    declarative (0.0.20)
    devise (4.9.0)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_token_auth (1.2.5)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 8.1)
    devise_uid (0.1.1)
      devise (>= 3.0.0)
      railties (>= 3.0)
    diff-lcs (1.5.0)
    e2mmap (0.1.0)
    erb (5.0.1)
    erubi (1.12.0)
    et-orbi (1.2.7)
      tzinfo
    faraday (2.7.10)
      faraday-net_http (>= 2.0, < 3.1)
      ruby2_keywords (>= 0.0.4)
    faraday-net_http (3.0.2)
    fcm (1.0.8)
      faraday (>= 1.0.0, < 3.0)
      googleauth (~> 1)
    fcmpush (1.4.4)
      google-apis-identitytoolkit_v3
      net-http-persistent (~> 4.0.1)
    ffi (1.15.5)
    fugit (1.9.0)
      et-orbi (~> 1, >= 1.2.7)
      raabro (~> 1.4)
    globalid (1.1.0)
      activesupport (>= 5.0)
    google-apis-core (0.11.3)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-identitytoolkit_v3 (0.14.0)
      google-apis-core (>= 0.11.0, < 2.a)
    googleauth (1.8.0)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hkdf (0.3.0)
    htmlentities (4.3.4)
    http-2 (0.11.0)
    httpclient (2.8.3)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jaro_winkler (1.5.4)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.6.3)
    jwt (2.7.1)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    loofah (2.19.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    method_source (1.0.0)
    mini_magick (4.12.0)
    mini_mime (1.1.2)
    minitest (5.18.0)
    msgpack (1.6.1)
    multi_json (1.15.0)
    mysql2 (0.5.5)
    nested_form (0.3.2)
    net-http-persistent (4.0.2)
      connection_pool (~> 2.2)
    net-http2 (0.18.5)
      http-2 (~> 0.11)
    net-imap (0.3.4)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-smtp (0.3.3)
      net-protocol
    nio4r (2.5.8)
    nokogiri (1.14.2-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.14.2-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.14.2-x86_64-linux)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    os (1.1.4)
    parallel (1.22.1)
    parser (3.2.1.1)
      ast (~> 2.4.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (5.0.3)
    puma (6.3.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.6.2)
    rack (2.2.6.3)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-test (2.0.2)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    rails_admin (3.1.1)
      activemodel-serializers-xml (>= 1.0)
      kaminari (>= 0.14, < 2.0)
      nested_form (~> 0.3)
      rails (>= 6.0, < 8)
      turbo-rails (~> 1.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.0.6)
    rbs (2.8.2)
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    redis (5.0.7)
      redis-client (>= 0.9.0)
    redis-client (0.14.1)
      connection_pool
    regexp_parser (2.7.0)
    reline (0.6.1)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    retriable (3.1.2)
    reverse_markdown (2.1.1)
      nokogiri
    rexml (3.2.5)
    roo (2.10.0)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    rpush (7.0.1)
      activesupport (>= 5.2)
      jwt (>= 1.5.6)
      multi_json (~> 1.0)
      net-http-persistent
      net-http2 (~> 0.18, >= 0.18.3)
      railties
      rainbow
      thor (>= 0.18.1, < 2.0)
      webpush (~> 1.0)
    rubocop (1.48.1)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.26.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.27.0)
      parser (>= *******)
    rubocop-rails (2.18.0)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
    ruby-progressbar (1.13.0)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    ruby_http_client (3.5.5)
    rubyzip (2.3.2)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    sentry-rails (5.18.2)
      railties (>= 5.0)
      sentry-ruby (~> 5.18.2)
    sentry-ruby (5.18.2)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sidekiq (6.5.5)
      connection_pool (>= 2.2.2)
      rack (~> 2.0)
      redis (>= 4.5.0)
    sidekiq-cron (1.12.0)
      fugit (~> 1.8)
      globalid (>= 1.0.1)
      sidekiq (>= 6)
    signet (0.18.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    solargraph (0.49.0)
      backport (~> 1.2)
      benchmark
      bundler (~> 2.0)
      diff-lcs (~> 1.4)
      e2mmap
      jaro_winkler (~> 1.5)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      parser (~> 3.0)
      rbs (~> 2.0)
      reverse_markdown (~> 2.0)
      rubocop (~> 1.38)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
    sprockets (4.2.0)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stackprof (0.2.26)
    statsd-ruby (1.5.0)
    stringio (3.1.7)
    thor (1.2.1)
    tilt (2.1.0)
    timeout (0.3.2)
    trailblazer-option (0.1.2)
    turbo-rails (1.4.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (2.4.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    webpush (1.1.0)
      hkdf (~> 0.2)
      jwt (~> 2.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    yard (0.9.34)
    zeitwerk (2.6.7)

PLATFORMS
  aarch64-linux
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  aws-sdk-s3
  barnes
  bootsnap
  caxlsx
  caxlsx_rails
  debug
  devise
  devise_token_auth (~> 1.2, >= 1.2.5)
  devise_uid
  faraday
  fcm
  fcmpush
  image_processing (~> 1.2)
  jbuilder
  kaminari
  mysql2 (~> 0.5.5)
  puma (~> 6.3)
  rack-cors
  rails (~> 7.0.4, >= *******)
  rails_admin (~> 3.1, >= 3.1.1)
  redis
  redis-client
  roo
  rpush
  rubocop-rails
  rubyzip
  sassc-rails
  sendgrid-ruby
  sentry-rails
  sentry-ruby
  sidekiq (~> 6.5)
  sidekiq-cron
  solargraph
  stackprof
  tzinfo-data

RUBY VERSION
   ruby 3.2.2p53

BUNDLED WITH
   2.4.12
