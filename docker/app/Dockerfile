FROM ruby:3.2.2-slim

RUN apt-get update -qq && apt-get install -yq --no-install-recommends \
  build-essential \
  gnupg2 \
  curl \
  less \
  git \
  libvips \
  libpq-dev \
  default-mysql-client \
  redis-tools \
  vim \
  libssl-dev \
  libghc-zlib-dev \
  libcurl4-gnutls-dev \
  libexpat1-dev \
  libgnutls30 \
  ffmpeg \
  openssh-client \
  libvips42 \
  default-libmysqlclient-dev \
  && apt-get clean \
  && rm -rf /var/cache/apt/archives/* \
  && truncate -s 0 /var/log/*log \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Add Node.js to sources list
RUN curl -sL https://deb.nodesource.com/setup_18.x | bash -

# Install Node.js version that will enable installation of yarn
RUN apt-get install -y --no-install-recommends \
  nodejs \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

LABEL maintainer="<EMAIL>"

# Enforce cachebusting
ENV version=2

#testing

# set the timezone to Asia/Kolkata
ENV TZ=Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Configure bundler
ENV LANG=C.UTF-8 \
  BUNDLE_JOBS=4 \
  BUNDLE_RETRY=3

# RUN npm install -g yarn

RUN gem update --system && gem install bundler

WORKDIR /usr/src/app
