# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_07_08_110840) do
  create_table "active_storage_attachments", charset: "utf8mb3", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", charset: "utf8mb3", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "assignments", charset: "utf8mb3", force: :cascade do |t|
    t.string "assignable_type", null: false
    t.bigint "assignable_id", null: false
    t.bigint "user_id", null: false
    t.integer "role_type", null: false
    t.integer "status", default: 0, null: false
    t.datetime "assigned_at", null: false
    t.datetime "removed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assignable_type", "assignable_id"], name: "index_assignments_on_assignable"
    t.index ["user_id"], name: "index_assignments_on_user_id"
  end

  create_table "bookings", charset: "utf8mb3", force: :cascade do |t|
    t.string "type", default: "Booking", null: false
    t.bigint "brand_id"
    t.bigint "brand_dealer_id"
    t.string "brand_booking_code"
    t.date "brand_booking_date"
    t.bigint "brand_model_id"
    t.string "model_variant"
    t.string "model_int_color"
    t.string "model_ext_color"
    t.date "ecb_receive_date"
    t.string "customer_name"
    t.string "contact_number"
    t.string "alt_contact_number"
    t.string "email"
    t.string "booking_address"
    t.string "address_line1"
    t.string "address_line2"
    t.string "locality"
    t.bigint "city_id"
    t.bigint "zone_id"
    t.bigint "state_id"
    t.string "pincode"
    t.decimal "latitude", precision: 15, scale: 10
    t.decimal "longitude", precision: 15, scale: 10
    t.boolean "is_outstation"
    t.string "company_name"
    t.string "gst"
    t.string "site_poc_name"
    t.string "site_poc_contact"
    t.string "ecb_booking_code"
    t.string "ecb_survey_code"
    t.string "ecb_install_code"
    t.datetime "scheduled_survey_date_time"
    t.datetime "actual_survey_date_time"
    t.datetime "scheduled_install_date_time"
    t.datetime "actual_install_date_time"
    t.datetime "survey_completed_at"
    t.datetime "install_completed_at"
    t.bigint "assigned_city_manager_id"
    t.bigint "assigned_field_agent1_id"
    t.bigint "assigned_field_agent2_id"
    t.bigint "assigned_partner_user_id"
    t.bigint "survey_partner_id"
    t.bigint "install_partner_id"
    t.bigint "booking_id"
    t.bigint "survey_id"
    t.boolean "customer_survey_confirmed", default: false
    t.boolean "customer_install_confirmed", default: false
    t.integer "status", default: 0, null: false
    t.string "on_hold_reason"
    t.string "cancel_reason"
    t.datetime "cancelled_at"
    t.datetime "first_contact_at"
    t.boolean "customer_ok_with_recording", default: false
    t.string "remarks"
    t.datetime "next_follow_up_at"
    t.datetime "survey_report_sent_at"
    t.datetime "approved_at"
    t.bigint "approved_by_id"
    t.datetime "layout_approved_at"
    t.bigint "layout_approved_by_id"
    t.date "charger_issued_date"
    t.string "charger_capacity"
    t.string "charger_make_type"
    t.string "cable_length"
    t.string "sanctioned_load"
    t.string "vdc"
    t.string "flag"
    t.string "mdi"
    t.string "vin"
    t.string "engine_number"
    t.boolean "extra_cable_customer_pay", default: false
    t.integer "install_type"
    t.boolean "survey_done_same_day", default: false
    t.string "cable_gauge"
    t.string "summary_stage1"
    t.string "summary_stage2"
    t.string "agent_notes"
    t.string "note_for_agent"
    t.boolean "customer_billing"
    t.datetime "customer_signed_at"
    t.integer "customer_rating"
    t.string "customer_feedback"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "dealer_name", limit: 100
    t.string "dealer_location", limit: 100
    t.string "model_colour", limit: 100
    t.string "model_description", limit: 512
    t.integer "brand_booking_status", default: 0
    t.string "sendbird_channel"
    t.string "obc"
    t.string "customer_city"
    t.string "charger_serial_number"
    t.decimal "avg_load_utilized", precision: 10, scale: 2
    t.bigint "uploaded_by_id"
    t.string "type_of_power_cable"
    t.string "core_of_power_cable"
    t.string "mcb_used_on_site"
    t.string "mcb_box_used_on_site"
    t.string "old_survey_number"
    t.decimal "cable_length_new", precision: 10, scale: 2
    t.string "ecb_complaint_code"
    t.string "ecb_visit_code"
    t.string "old_visit_number"
    t.string "brand_complaint_number"
    t.datetime "scheduled_visit_date_time"
    t.datetime "actual_visit_date_time"
    t.datetime "visit_completed_at"
    t.datetime "visit_report_sent_at"
    t.datetime "complaint_receive_date"
    t.boolean "customer_visit_confirmed", default: false
    t.boolean "visit_done_same_day", default: false
    t.string "close_reason"
    t.text "close_complaint_summary"
    t.bigint "complaint_id"
    t.bigint "visit_id"
    t.bigint "visit_partner_id"
    t.boolean "is_outstation_for_client"
    t.datetime "complaint_closed_at"
    t.string "visit_remarks"
    t.string "complaint_brand_model_name"
    t.string "survey_for_charger_type"
    t.text "surveyor_name"
    t.boolean "is_extra_material_used", default: false
    t.boolean "is_no_show_during_survey", default: false
    t.boolean "is_no_show_during_installation", default: false
    t.string "commission_number"
    t.float "pricing_per_meter"
    t.float "complementary_cable_length"
    t.boolean "payment_received", default: false
    t.string "invoice_number"
    t.datetime "invoice_date"
    t.float "invoice_amount"
    t.integer "nature_of_work"
    t.float "old_cable_used_length"
    t.float "new_cable_used_length"
    t.float "number_of_old_mcb_rcbo_used"
    t.float "number_of_new_mcb_rcbo_used"
    t.float "number_of_old_mcb_rcbo_box_used"
    t.float "number_of_new_mcb_rcbo_box_used"
    t.string "nature_of_install_remarks"
    t.boolean "bhe"
    t.date "bhe_date"
    t.string "payment_remarks"
    t.boolean "is_sow_completed"
    t.string "dealer_email"
    t.string "cee_name"
    t.date "cee_contact_date"
    t.string "city_manager_email"
    t.string "zonal_manager_email"
    t.string "survey_final_video_link"
    t.string "installation_final_video_link"
    t.string "installation_clv_video_link"
    t.boolean "seek_payment_email_sent"
    t.boolean "refund_email_sent"
    t.boolean "refund_processed_email_sent"
    t.date "commission_date"
    t.date "expected_delivery_date"
    t.integer "sow_status"
    t.date "sow_completion_date"
    t.integer "extra_cable_payment_status"
    t.date "extra_cable_payment_date"
    t.date "survey_readiness_information_date"
    t.date "installation_readiness_information_date"
    t.date "booking_status_updated_date"
    t.date "edd_status_updated_date"
    t.text "invoice_remarks"
    t.string "dealer_gst"
    t.date "customer_proposed_survey_date"
    t.date "customer_proposed_survey_date_updated_at"
    t.date "customer_proposed_install_date"
    t.date "customer_proposed_install_date_updated_at"
    t.index ["approved_by_id"], name: "index_bookings_on_approved_by_id"
    t.index ["assigned_city_manager_id"], name: "index_bookings_on_assigned_city_manager_id"
    t.index ["assigned_field_agent1_id"], name: "index_bookings_on_assigned_field_agent1_id"
    t.index ["assigned_field_agent2_id"], name: "index_bookings_on_assigned_field_agent2_id"
    t.index ["assigned_partner_user_id"], name: "index_bookings_on_assigned_partner_user_id"
    t.index ["booking_id"], name: "index_bookings_on_booking_id"
    t.index ["brand_dealer_id"], name: "index_bookings_on_brand_dealer_id"
    t.index ["brand_id"], name: "index_bookings_on_brand_id"
    t.index ["brand_model_id"], name: "index_bookings_on_brand_model_id"
    t.index ["city_id"], name: "index_bookings_on_city_id"
    t.index ["complaint_id"], name: "index_bookings_on_complaint_id"
    t.index ["install_partner_id"], name: "index_bookings_on_install_partner_id"
    t.index ["layout_approved_by_id"], name: "index_bookings_on_layout_approved_by_id"
    t.index ["state_id"], name: "index_bookings_on_state_id"
    t.index ["survey_id"], name: "index_bookings_on_survey_id"
    t.index ["survey_partner_id"], name: "index_bookings_on_survey_partner_id"
    t.index ["uploaded_by_id"], name: "index_bookings_on_uploaded_by_id"
    t.index ["visit_id"], name: "index_bookings_on_visit_id"
    t.index ["visit_partner_id"], name: "index_bookings_on_visit_partner_id"
    t.index ["zone_id"], name: "index_bookings_on_zone_id"
  end

  create_table "brand_dealers", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "brand_id"
    t.bigint "location_id"
    t.string "name", null: false
    t.string "company_name"
    t.string "code"
    t.string "branch_code"
    t.string "contact_person"
    t.string "contact_number"
    t.string "alt_contact_number"
    t.string "gst"
    t.string "address"
    t.string "billing_address"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_brand_dealers_on_brand_id"
    t.index ["location_id"], name: "index_brand_dealers_on_location_id"
  end

  create_table "brand_model_pricings", charset: "utf8mb3", force: :cascade do |t|
    t.float "pricing_per_meter"
    t.float "complementary_cable_length"
    t.datetime "start_date"
    t.datetime "end_date"
    t.bigint "brand_model_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "charger_size"
    t.index ["brand_model_id"], name: "index_brand_model_pricings_on_brand_model_id"
  end

  create_table "brand_models", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "brand_id"
    t.string "name", null: false
    t.string "code", null: false
    t.integer "charger_size", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_required_in_payment_tracker", default: false
    t.index ["brand_id"], name: "index_brand_models_on_brand_id"
  end

  create_table "brands", charset: "utf8mb3", force: :cascade do |t|
    t.string "name", null: false
    t.integer "status", default: 0, null: false
    t.string "code", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "contact_person"
    t.string "contact_number"
    t.string "alt_contact_number"
    t.string "gst"
    t.string "billing_address"
    t.text "terms_and_conditions"
    t.boolean "add_to_forms", default: true, null: false
    t.boolean "automated_emails_enabled", default: false
  end

  create_table "charger_types", charset: "utf8mb3", force: :cascade do |t|
    t.integer "charger_size", default: 0, null: false
    t.text "seek_payment_email_template"
    t.text "refund_email_template"
    t.text "refund_processed_email_template"
    t.bigint "brand_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_charger_types_on_brand_id"
  end

  create_table "customer_connect_logs", charset: "utf8mb3", force: :cascade do |t|
    t.string "loggable_type", null: false
    t.bigint "loggable_id", null: false
    t.bigint "user_id", null: false
    t.integer "log_type", null: false
    t.text "summary"
    t.boolean "call_connected", default: false
    t.datetime "next_follow_up_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["loggable_type", "loggable_id"], name: "index_customer_connect_logs_on_loggable"
    t.index ["user_id"], name: "index_customer_connect_logs_on_user_id"
  end

  create_table "form_question_brands", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "form_question_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "brand_model_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_form_question_brands_on_brand_id"
    t.index ["brand_model_id"], name: "index_form_question_brands_on_brand_model_id"
    t.index ["form_question_id"], name: "index_form_question_brands_on_form_question_id"
  end

  create_table "form_questions", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "form_section_id", null: false
    t.string "title", null: false
    t.string "description"
    t.integer "question_type", default: 0, null: false
    t.string "choices", default: "--- []\n"
    t.integer "image_upload", default: 0, null: false
    t.integer "video_upload", default: 0, null: false
    t.integer "position", default: 0, null: false
    t.boolean "add_to_report", default: false, null: false
    t.boolean "mandatory", default: false, null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "linked_field"
    t.boolean "add_to_customer_report", default: false, null: false
    t.string "autofill_from"
    t.index ["form_section_id"], name: "index_form_questions_on_form_section_id"
  end

  create_table "form_section_brands", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "form_section_id", null: false
    t.bigint "brand_id", null: false
    t.bigint "brand_model_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_form_section_brands_on_brand_id"
    t.index ["brand_model_id"], name: "index_form_section_brands_on_brand_model_id"
    t.index ["form_section_id"], name: "index_form_section_brands_on_form_section_id"
  end

  create_table "form_sections", charset: "utf8mb3", force: :cascade do |t|
    t.string "name", null: false
    t.integer "section_type", default: 0, null: false
    t.integer "position", default: 0, null: false
    t.boolean "add_to_report", default: false, null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "use_in_survey", default: false
    t.boolean "use_in_installation", default: false
    t.boolean "use_in_visit", default: false
  end

  create_table "form_submissions", charset: "utf8mb3", force: :cascade do |t|
    t.string "formable_type", null: false
    t.bigint "formable_id", null: false
    t.bigint "form_section_id", null: false
    t.bigint "form_question_id", null: false
    t.string "response"
    t.string "section_name", null: false
    t.string "question_title", null: false
    t.integer "position", null: false
    t.boolean "add_to_report", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["form_question_id"], name: "index_form_submissions_on_form_question_id"
    t.index ["form_section_id"], name: "index_form_submissions_on_form_section_id"
    t.index ["formable_type", "formable_id"], name: "index_form_submissions_on_formable"
  end

  create_table "locations", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "parent_id"
    t.string "name", null: false
    t.string "code"
    t.integer "location_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parent_id"], name: "index_locations_on_parent_id"
  end

  create_table "partner_locations", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "partner_id"
    t.bigint "location_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["location_id"], name: "index_partner_locations_on_location_id"
    t.index ["partner_id"], name: "index_partner_locations_on_partner_id"
  end

  create_table "partners", charset: "utf8mb3", force: :cascade do |t|
    t.string "name", null: false
    t.string "company_name", null: false
    t.string "code"
    t.string "contact_person"
    t.string "contact_number"
    t.string "alt_contact_number"
    t.string "gst"
    t.string "billing_address"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "payments", charset: "utf8mb3", force: :cascade do |t|
    t.string "payment_ref_number"
    t.datetime "payment_date"
    t.float "payment_amount"
    t.string "payment_party_code"
    t.string "bank_name"
    t.string "type", default: "Payment", null: false
    t.string "payable_type", null: false
    t.bigint "payable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "payment_description"
    t.string "payment_gateway_id"
    t.string "customer_vpa"
    t.string "customer_bank"
    t.string "payment_rrn"
    t.index ["payable_type", "payable_id"], name: "index_payments_on_payable"
  end

  create_table "rpush_apps", charset: "utf8mb3", force: :cascade do |t|
    t.string "name", null: false
    t.string "environment"
    t.text "certificate"
    t.string "password"
    t.integer "connections", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type", null: false
    t.string "auth_key"
    t.string "client_id"
    t.string "client_secret"
    t.string "access_token"
    t.datetime "access_token_expiration"
    t.text "apn_key"
    t.string "apn_key_id"
    t.string "team_id"
    t.string "bundle_id"
    t.boolean "feedback_enabled", default: true
  end

  create_table "rpush_feedback", charset: "utf8mb3", force: :cascade do |t|
    t.string "device_token"
    t.timestamp "failed_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "app_id"
    t.index ["device_token"], name: "index_rpush_feedback_on_device_token"
  end

  create_table "rpush_notifications", charset: "utf8mb3", force: :cascade do |t|
    t.integer "badge"
    t.string "device_token"
    t.string "sound"
    t.text "alert"
    t.text "data"
    t.integer "expiry", default: 86400
    t.boolean "delivered", default: false, null: false
    t.timestamp "delivered_at"
    t.boolean "failed", default: false, null: false
    t.timestamp "failed_at"
    t.integer "error_code"
    t.text "error_description"
    t.timestamp "deliver_after"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "alert_is_json", default: false, null: false
    t.string "type", null: false
    t.string "collapse_key"
    t.boolean "delay_while_idle", default: false, null: false
    t.text "registration_ids", size: :medium
    t.integer "app_id", null: false
    t.integer "retries", default: 0
    t.string "uri"
    t.timestamp "fail_after"
    t.boolean "processing", default: false, null: false
    t.integer "priority"
    t.text "url_args"
    t.string "category"
    t.boolean "content_available", default: false, null: false
    t.text "notification"
    t.boolean "mutable_content", default: false, null: false
    t.string "external_device_id"
    t.string "thread_id"
    t.boolean "dry_run", default: false, null: false
    t.boolean "sound_is_json", default: false
    t.index ["delivered", "failed", "processing", "deliver_after", "created_at"], name: "index_rpush_notifications_multi"
  end

  create_table "user_fcm_registrations", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "fcm_registration_token", null: false
    t.string "device_id"
    t.string "device_os"
    t.string "device_os_version"
    t.string "device_name"
    t.string "device_app_version"
    t.string "device_app_build_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_fcm_registrations_on_user_id"
  end

  create_table "user_roles", charset: "utf8mb3", force: :cascade do |t|
    t.bigint "user_id"
    t.integer "role_type", default: 0, null: false
    t.bigint "location_id"
    t.bigint "brand_id"
    t.bigint "partner_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["brand_id"], name: "index_user_roles_on_brand_id"
    t.index ["location_id"], name: "index_user_roles_on_location_id"
    t.index ["partner_id"], name: "index_user_roles_on_partner_id"
    t.index ["user_id"], name: "index_user_roles_on_user_id"
  end

  create_table "users", charset: "utf8mb3", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "name", null: false
    t.string "mobile", null: false
    t.string "alternate_mobile"
    t.integer "user_type", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uid"
    t.string "provider", default: "email", null: false
    t.boolean "allow_password_change", default: false
    t.string "nickname"
    t.string "image"
    t.text "tokens"
    t.string "employee_id"
    t.string "aadhaar"
    t.string "sendbird_token"
    t.bigint "brand_id"
    t.index ["brand_id"], name: "index_users_on_brand_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["uid", "provider"], name: "index_users_on_uid_and_provider", unique: true
    t.index ["uid"], name: "index_users_on_uid", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "bookings", "bookings"
  add_foreign_key "bookings", "bookings", column: "complaint_id"
  add_foreign_key "bookings", "bookings", column: "survey_id"
  add_foreign_key "bookings", "bookings", column: "visit_id"
  add_foreign_key "bookings", "locations", column: "city_id"
  add_foreign_key "bookings", "locations", column: "state_id"
  add_foreign_key "bookings", "locations", column: "zone_id"
  add_foreign_key "bookings", "partners", column: "install_partner_id"
  add_foreign_key "bookings", "partners", column: "survey_partner_id"
  add_foreign_key "bookings", "partners", column: "visit_partner_id"
  add_foreign_key "bookings", "users", column: "approved_by_id"
  add_foreign_key "bookings", "users", column: "assigned_city_manager_id"
  add_foreign_key "bookings", "users", column: "assigned_field_agent1_id"
  add_foreign_key "bookings", "users", column: "assigned_field_agent2_id"
  add_foreign_key "bookings", "users", column: "assigned_partner_user_id"
  add_foreign_key "bookings", "users", column: "layout_approved_by_id"
  add_foreign_key "bookings", "users", column: "uploaded_by_id"
  add_foreign_key "brand_model_pricings", "brand_models"
  add_foreign_key "charger_types", "brands"
  add_foreign_key "form_question_brands", "brands"
  add_foreign_key "form_question_brands", "form_questions"
  add_foreign_key "form_questions", "form_sections"
  add_foreign_key "form_section_brands", "brands"
  add_foreign_key "form_section_brands", "form_sections"
  add_foreign_key "form_submissions", "form_questions"
  add_foreign_key "form_submissions", "form_sections"
  add_foreign_key "locations", "locations", column: "parent_id"
  add_foreign_key "user_fcm_registrations", "users"
  add_foreign_key "users", "brands"
end
