# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create Zone
zone = Location.find_or_initialize_by(name: "East", code: "EAST", location_type: "zone")
zone.save

# Create State
state = Location.find_or_initialize_by(name: "Haryana", code: "HR", location_type: "state")
state.parent_id = zone.id
state.save

# Create Location
city = Location.find_or_initialize_by(name: "Gurugram", code: "GRG", location_type: "city")
city.parent_id = state.id
city.save

# Create Brand
brand = Brand.find_or_initialize_by(name: "Hyundai", code: "HYN")
brand.save

# Create BrandModel
brand_model = BrandModel.find_or_initialize_by(name: "Venue S Optional", code: "VENUESOPT", brand: brand)
brand_model.save

# Create Partner
partner = Partner.find_or_initialize_by(name: "Partner 1", code: "P1", company_name: "Partner 1 Company")
partner.save

# Create PartnerLocation
partner_location = PartnerLocation.find_or_initialize_by(partner: partner, location: city)
partner_location.save

# Create Super User
super_user = User.find_or_initialize_by(name: "Super User", mobile: 9876543210, email: "<EMAIL>")
super_user.password = 9876543210
super_user.save
super_user.user_roles.find_or_initialize_by(role_type: :super_user).save

# Create MIS User
mis_user = User.find_or_initialize_by(name: "MIS User", mobile: 9999998888, email: "<EMAIL>")
mis_user.password = 9999998888
mis_user.save
mis_user.user_roles.find_or_initialize_by(role_type: :mis).save

# Create Management User
mgmt_user = User.find_or_initialize_by(name: "MGMT User", mobile: 9999999888, email: "<EMAIL>")
mgmt_user.password = 9999999888
mgmt_user.save
mgmt_user.user_roles.find_or_initialize_by(role_type: :management).save

# Create Zonal Manager User
zonal_manager = User.find_or_initialize_by(name: "ZM 1", mobile: 9999988888, email: "<EMAIL>")
zonal_manager.password = 9999988888
zonal_manager.save
zonal_manager.user_roles.find_or_initialize_by(role_type: :zonal_manager, location_id: zone.id).save

# Create City Manager User
city_manager = User.find_or_initialize_by(name: "CM 1", mobile: 9999977777, email: "<EMAIL>")
city_manager.password = 9999977777
city_manager.save
city_manager.user_roles.find_or_initialize_by(role_type: :city_manager, location_id: city.id).save

# Create Partner Manager User
partner_manager = User.find_or_initialize_by(name: "Partner Manager 1", mobile: 9999966666, email: "<EMAIL>", user_type: "external")
partner_manager.password = 9999966666
partner_manager.save
partner_manager.user_roles.find_or_initialize_by(role_type: :partner_manager, location_id: city.id, partner_id: partner.id).save

# Create Field Agent User
field_agent = User.find_or_initialize_by(name: "Field Agent 1", mobile: 9999955555, email: "<EMAIL>", user_type: "external")
field_agent.password = 9999955555
field_agent.save
field_agent.user_roles.find_or_initialize_by(role_type: :field_agent, location_id: city.id, partner_id: partner.id, brand_id: brand.id).save

# Create Finance User
finance_user = User.find_or_initialize_by(name: "Finance User", mobile: 9999944444, email: "<EMAIL>")
finance_user.password = 9999944444
finance_user.save
finance_user.user_roles.find_or_initialize_by(role_type: :finance_controller).save
