class CreateBrandDealer < ActiveRecord::Migration[7.0]
  def change
    create_table :brand_dealers do |t|
      t.references :brand, index: true
      t.references :location, index: true
      t.string :name, null: false
      t.string :company_name
      t.string :code
      t.string :branch_code
      t.string :contact_person
      t.string :contact_number
      t.string :alt_contact_number
      t.string :gst
      t.string :address
      t.string :billing_address
      t.integer :status, default: 0, null: false

      t.timestamps
    end
  end
end
