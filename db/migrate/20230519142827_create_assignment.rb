class CreateAssignment < ActiveRecord::Migration[7.0]
  def change
    create_table :assignments do |t|
      t.references :assignable, polymorphic: true, null: false, index: true
      t.references :user, index: true, null: false
      t.integer :role_type, null: false
      t.integer :status, default: 0, null: false
      t.datetime :assigned_at, null: false
      t.datetime :removed_at

      t.timestamps
    end
  end
end


