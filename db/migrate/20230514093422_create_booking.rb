class CreateBooking < ActiveRecord::Migration[7.0]
  def change
    create_table :bookings do |t|
      t.string :type, null: false, default: 'Booking'
      t.references :brand, index: true
      t.references :brand_dealer, index: true
      t.string :brand_booking_code
      t.date :brand_booking_date
      t.references :brand_model, index: true
      t.string :model_variant
      t.string :model_int_color
      t.string :model_ext_color
      t.date :ecb_receive_date

      t.string :customer_name
      t.string :contact_number
      t.string :alt_contact_number
      t.string :email
      t.string :booking_address
      t.string :address_line1
      t.string :address_line2
      t.string :locality
      t.references :city, index: true, foreign_key: { to_table: :locations }
      t.references :zone, index: true, foreign_key: { to_table: :locations }
      t.references :state, index: true, foreign_key: { to_table: :locations }
      t.string :pincode
      t.decimal :latitude, precision: 15, scale: 10
      t.decimal :longitude, precision: 15, scale: 10
      t.boolean :is_outstation, default: false
      t.string :company_name
      t.string :gst
      t.string :site_poc_name
      t.string :site_poc_contact

      t.string :ecb_booking_code
      t.string :ecb_survey_code
      t.string :ecb_install_code
      t.datetime :schduled_survey_date_time
      t.datetime :actual_survey_date_time
      t.datetime :schduled_install_date_time
      t.datetime :actual_install_date_time
      t.datetime :survey_completed_at
      t.datetime :install_completed_at

      t.references :assigned_city_manager, index: true, foreign_key: { to_table: :users }
      t.references :assigned_field_agent1, index: true, foreign_key: { to_table: :users }
      t.references :assigned_field_agent2, index: true, foreign_key: { to_table: :users }
      t.references :assigned_partner_user, index: true, foreign_key: { to_table: :users }
      t.references :survey_partner, index: true, foreign_key: { to_table: :partners }
      t.references :install_partner, index: true, foreign_key: { to_table: :partners }
      t.references :booking, index: true, foreign_key: { to_table: :bookings }
      t.references :survey, index: true, foreign_key: { to_table: :bookings }

      t.boolean :customer_survey_confirmed, default: false
      t.boolean :customer_install_confirmed, default: false
      t.integer :status, default: 0, null: false
      t.string :on_hold_reason
      t.string :cancel_reason
      t.datetime :cancelled_at
      t.datetime :first_contact_at
      t.boolean :customer_ok_with_recording, default: false
      t.string :remarks
      t.datetime :next_follow_up_at
      t.datetime :survey_report_sent_at
      t.datetime :approved_at
      t.references :approved_by, index: true, foreign_key: { to_table: :users }
      t.datetime :layout_approved_at
      t.references :layout_approved_by, index: true, foreign_key: { to_table: :users }
      t.date :charger_issued_date

      t.string :charger_capacity
      t.string :charger_make_type
      t.decimal :cable_length, precision: 10, scale: 2
      t.decimal :sanctioned_load, precision: 10, scale: 2
      t.string :vdc
      t.string :flag
      t.string :mdi
      t.string :vin
      t.string :engine_number
      t.boolean :extra_cable_customer_pay, default: false
      t.integer :install_type
      t.boolean :survey_done_same_day, default: false
      t.string :cable_gauge

      t.string :summary_stage1
      t.string :summary_stage2
      t.string :agent_notes
      t.string :note_for_agent

      t.boolean :customer_billing, default: false
      t.datetime :customer_signed_at
      t.integer :customer_rating
      t.string :customer_feedback

      t.timestamps
    end
  end
end
