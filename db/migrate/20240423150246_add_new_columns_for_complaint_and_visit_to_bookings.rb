class AddNewColumnsForComplaintAndVisitToBookings < ActiveRecord::Migration[7.0]
  def change
    add_column :bookings, :ecb_complaint_code, :string
    add_column :bookings, :ecb_visit_code, :string
    add_column :bookings, :old_visit_number, :string
    add_column :bookings, :brand_complaint_number, :string
    add_column :bookings, :scheduled_visit_date_time, :datetime
    add_column :bookings, :actual_visit_date_time, :datetime
    add_column :bookings, :visit_completed_at, :datetime
    add_column :bookings, :visit_report_sent_at, :datetime
    add_column :bookings, :complaint_receive_date, :datetime
    add_column :bookings, :customer_visit_confirmed, :boolean, default: false
    add_column :bookings, :visit_done_same_day, :boolean, default: false
    add_column :bookings, :close_reason, :string
    add_column :bookings, :close_complaint_summary, :text

    add_reference :bookings, :complaint, index: true, foreign_key: { to_table: :bookings }
    add_reference :bookings, :visit, index: true, foreign_key: { to_table: :bookings }
    add_reference :bookings, :visit_partner, index: true, foreign_key: { to_table: :partners }
  end
end
