class CreateUserFcmRegistration < ActiveRecord::Migration[7.0]
  def change
    create_table :user_fcm_registrations do |t|
      t.references :user, null: false, foreign_key: true, index: true
      t.string :fcm_registration_token, null: false
      t.string :device_id
      t.string :device_os
      t.string :device_os_version
      t.string :device_name
      t.string :device_app_version
      t.string :device_app_build_number

      t.timestamps
    end
  end
end
