class CreatePartner < ActiveRecord::Migration[7.0]
  def change
    create_table :partners do |t|
      t.string :name, null: false
      t.string :company_name, null: false
      t.string :code
      t.string :contact_person
      t.string :contact_number
      t.string :alt_contact_number
      t.string :gst
      t.string :billing_address
      t.integer :status, default: 0, null: false

      t.timestamps
    end
  end
end
