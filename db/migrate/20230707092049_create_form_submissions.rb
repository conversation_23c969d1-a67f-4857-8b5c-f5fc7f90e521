class CreateFormSubmissions < ActiveRecord::Migration[7.0]
  def change
    create_table :form_submissions do |t|
      t.references :formable, polymorphic: true, null: false, index: true
      t.references :form_section, index: true, null: false, foreign_key: true
      t.references :form_question, index: true, null: false, foreign_key: true
      t.string :response
      t.string :section_name, null: false
      t.string :question_title, null: false
      t.integer :position, null: false
      t.boolean :add_to_report, null: false
      # images
      # videos
      t.timestamps
    end
  end
end
