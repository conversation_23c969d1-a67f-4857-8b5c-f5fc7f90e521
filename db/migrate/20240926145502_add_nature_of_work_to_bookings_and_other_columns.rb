class AddNatureOfWorkToBookingsAndOtherColumns < ActiveRecord::Migration[7.0]
  def up
    add_column :bookings, :nature_of_work, :int
    add_column :bookings, :old_cable_used_length, :float
    add_column :bookings, :new_cable_used_length, :float
    add_column :bookings, :number_of_old_mcb_rcbo_used, :float
    add_column :bookings, :number_of_new_mcb_rcbo_used, :float
    add_column :bookings, :number_of_old_mcb_rcbo_box_used, :float
    add_column :bookings, :number_of_new_mcb_rcbo_box_used, :float
    add_column :bookings, :nature_of_install_remarks, :string
  end

  def down
    remove_column :bookings, :nature_of_work, :int
    remove_column :bookings, :old_cable_used_length, :float
    remove_column :bookings, :new_cable_used_length, :float
    remove_column :bookings, :number_of_old_mcb_rcbo_used, :float
    remove_column :bookings, :number_of_new_mcb_rcbo_used, :float
    remove_column :bookings, :number_of_old_mcb_rcbo_box_used, :float
    remove_column :bookings, :number_of_new_mcb_rcbo_box_used, :float
    remove_column :bookings, :nature_of_install_remarks, :string
  end
end
