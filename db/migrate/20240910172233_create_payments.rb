class CreatePayments < ActiveRecord::Migration[7.0]
  def change
    create_table :payments do |t|
      t.string :payment_ref_number
      t.datetime :payment_date
      t.float :payment_amount
      t.string :payment_party_code
      t.string :bank_name
      t.string :type, default: "Payment", null: false
      t.references :payable, polymorphic: true, null: false, index: true
      t.timestamps
    end
  end
end
