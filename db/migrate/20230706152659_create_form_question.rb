class CreateFormQuestion < ActiveRecord::Migration[7.0]
  def change
    create_table :form_questions do |t|
      t.references :form_section, index: true, null: false, foreign_key: true
      t.string :title, null: false
      t.string :description
      t.integer :question_type, default: 0, null: false
      t.string :choices, default: [].to_yaml
      t.integer :image_upload, default: 0, null: false
      t.integer :video_upload, default: 0, null: false
      t.integer :position, default: 0, null: false
      t.boolean :add_to_report, default: false, null: false
      t.boolean :mandatory, default: false, null: false
      t.integer :status, default: 0, null: false

      t.timestamps
    end
  end
end
