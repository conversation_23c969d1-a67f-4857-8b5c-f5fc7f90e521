class AddUniqueIndexToEcbSurveyCode < ActiveRecord::Migration[7.0]
  def change
    # First, remove any potential duplicates (optional - you might want to handle this manually)
    # This step ensures the unique constraint can be added successfully
    reversible do |dir|
      dir.up do
        # Find and log duplicates before adding the constraint
        duplicates = execute(<<-SQL
          SELECT ecb_survey_code, COUNT(*) as count
          FROM bookings
          WHERE ecb_survey_code IS NOT NULL
          AND ecb_survey_code != ''
          AND type = 'Survey'
          GROUP BY ecb_survey_code
          HAVING COUNT(*) > 1
        SQL
        ).to_a

        if duplicates.any?
          Rails.logger.warn "Found #{duplicates.length} duplicate ecb_survey_codes: #{duplicates.map { |d| d['ecb_survey_code'] }.join(', ')}"
          # You may want to handle these duplicates manually before running this migration
        end
      end
    end

    # Add unique index for Survey type records only
    # Manually done on mysql console

    # SELECT ecb_survey_code
    # FROM bookings
    # WHERE ecb_survey_code IS NOT NULL
    # AND ecb_survey_code != ''
    # AND type = 'Survey'
    # GROUP BY ecb_survey_code
    # HAVING COUNT(*) > 1;

    # SELECT ecb_install_code
    # FROM bookings
    # WHERE ecb_install_code IS NOT NULL
    # AND ecb_install_code != ''
    # AND type = 'Installation'
    # GROUP BY ecb_install_code
    # HAVING COUNT(*) > 1;

    # SELECT ecb_booking_code
    # FROM bookings
    # WHERE ecb_booking_code IS NOT NULL
    # AND ecb_booking_code != ''
    # AND type = 'Booking'
    # GROUP BY ecb_booking_code
    # HAVING COUNT(*) > 1;
    #
    # CREATE UNIQUE INDEX index_bookings_on_ecb_survey_code_unique
    # ON bookings ((CASE WHEN type = 'Survey' AND ecb_survey_code IS NOT NULL
    # THEN ecb_survey_code ELSE NULL END));
    #
    # CREATE UNIQUE INDEX index_bookings_on_ecb_install_code_unique
    # ON bookings ((CASE WHEN type = 'Installation' AND ecb_install_code IS NOT NULL
    # THEN ecb_install_code ELSE NULL END));
    #
    # CREATE UNIQUE INDEX index_bookings_on_ecb_booking_code_unique
    # ON bookings ((CASE WHEN type = 'Booking' AND ecb_booking_code IS NOT NULL
    # THEN ecb_booking_code ELSE NULL END));
    #
    # CREATE UNIQUE INDEX index_bookings_on_ecb_complaint_code_unique
    # ON bookings ((CASE WHEN type = 'Complaint' AND ecb_complaint_code IS NOT NULL
    # THEN ecb_complaint_code ELSE NULL END));

    # CREATE UNIQUE INDEX index_bookings_on_ecb_visit_code_unique
    # ON bookings ((CASE WHEN type = 'Visit' AND ecb_visit_code IS NOT NULL
    # THEN ecb_visit_code ELSE NULL END));
  end
end
