class CreateCustomerConnectLog < ActiveRecord::Migration[7.0]
  def change
    create_table :customer_connect_logs do |t|
      t.references :loggable, polymorphic: true, null: false, index: true
      t.references :user, index: true, null: false
      t.integer :log_type, null: false
      t.string :summary
      t.boolean :call_connected, default: false
      t.datetime :next_follow_up_at
      t.timestamps
    end
  end
end
