# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

# Ignore master key for decrypting credentials and more.
/config/master.key


.DS_Store
backup.sql
config/google_credentials.json
.env

ruby-debug*
roo-*
lambda/setu-qr-generator-node/node_modules/*
lambda/setu-qr-generator/vendor/*
vendor/*
lambda/setu-qr-generator-node/.aws-sam/*
lambda/setu-qr-generator-node/env.json
lambda/setu-qr-generator-node/env_prod.json
