# README -

Development setup with Dock<PERSON> 
Install docker desktop
In code directory run following:

- docker compose down
- docker compose build
- docker compose run --rm runner ./bin/setup
- docker compose up rails sidekiq


To add new secret: `EDITOR='code --wait' rails credentials:edit`

** Lambda Function **
Switch to lambda/setu-qr-generator-node directory
[*] Validate SAM template: sam validate
[*] To build lambda function: sam build
[*] Invoke Function: sam local invoke SetuQrGeneratorFunction -e event.json --env-vars env.json
[*] Test Function in the Cloud: sam sync --stack-name {{stack-name}} --watch
[*] Deploy: sam deploy --guided
