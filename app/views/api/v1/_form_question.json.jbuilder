json.extract! question, :id, :title, :description, :question_type, :position, :add_to_report, :add_to_customer_report, :mandatory, :image_upload,
              :video_upload, :choices, :status
json.question_brands question.form_question_brands, partial: 'api/v1/form_question_brand', as: :question_brand
if @form_responses && @form_responses[question.id].present?
  json.form_response @form_responses[question.id], partial: 'api/v1/form_response', as: :form_response
end
