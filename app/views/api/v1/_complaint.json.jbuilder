json.extract! complaint, :id, :brand_booking_code,
              :dealer_name, :dealer_location, :brand_booking_date, :model_variant, :model_colour, :model_description, :model_int_color,
              :model_ext_color, :ecb_receive_date, :customer_name, :contact_number,
              :alt_contact_number, :email, :booking_address, :address_line1, :address_line2, :locality,
              :pincode, :latitude, :longitude, :is_outstation, :company_name, :gst,
              :site_poc_name, :site_poc_contact, :ecb_booking_code,
              :customer_survey_confirmed, :status, :on_hold_reason, :cancel_reason, :close_reason, :close_complaint_summary,
              :cancelled_at, :first_contact_at, :customer_ok_with_recording, :remarks, :next_follow_up_at,
              :approved_at, :payment_remarks,
              :customer_billing, :created_at, :updated_at
json.brand do
  json.extract! complaint.brand, :terms_and_conditions, :id, :name, :code
  json.logo complaint.brand.brand_logo&.attachment&.url
end
# json.brand_dealer do
#   json.extract! complaint.brand_dealer, :id, :name, :code
# end
if complaint.brand_model
  json.brand_model do
    json.extract! complaint.brand_model, :id, :name, :code
  end
end
json.city do
  json.extract! complaint.city, :id, :name, :code
end
if complaint.zone
  json.zone do
    json.extract! complaint.zone, :id, :name, :code
  end
end
if complaint.state
  json.state do
    json.extract! complaint.state, :id, :name, :code
  end
end
if complaint.assigned_city_manager
  json.assigned_city_manager do
    json.extract! complaint.assigned_city_manager, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if complaint.approved_by
  json.approved_by do
    json.extract! complaint.approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
