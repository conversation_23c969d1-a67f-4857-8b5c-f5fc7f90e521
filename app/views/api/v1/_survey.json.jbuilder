json.extract! survey, :id,
              :dealer_name, :dealer_location, :model_variant, :model_colour, :model_description, :model_int_color,
              :model_ext_color, :customer_name, :contact_number,
              :alt_contact_number, :email, :booking_address, :address_line1, :address_line2, :locality,
              :pincode, :latitude, :longitude, :is_outstation, :is_outstation_for_client, :company_name, :gst,
              :site_poc_name, :site_poc_contact, :ecb_booking_code, :ecb_survey_code,
              :customer_survey_confirmed, :status, :on_hold_reason, :cancel_reason,
              :cancelled_at, :first_contact_at, :customer_ok_with_recording, :remarks, :next_follow_up_at,
              :approved_at,
              :customer_billing, :created_at, :updated_at,
              :note_for_agent, :extra_cable_customer_pay,
              :scheduled_survey_date_time, :payment_remarks,
              :summary_stage1, :summary_stage2, :agent_notes, :charger_capacity, :charger_make_type, :cable_length, :cable_length_new,
              :sanctioned_load, :vdc, :flag, :mdi, :cable_gauge, :obc, :customer_city, :avg_load_utilized, :payment_received,
              :amount_to_receive, :amount_to_refund, :invoice_total, :complementary_cable_length, :pricing_per_meter, :base_amount, :gst_amount, :payment_count, :refund_count,
              :seek_payment_email_preview, :refund_email_preview, :refund_processed_email_preview, :pricing_expired, :cee_name, :cee_contact_date, :dealer_email, :city_manager_email, :zonal_manager_email,
              :survey_final_video_link, :installation_final_video_link, :installation_clv_video_link,
              :seek_payment_email_sent, :refund_email_sent, :refund_processed_email_sent, :commission_date,
              :sow_status, :sow_completion_date, :extra_cable_payment_status,
              :extra_cable_payment_date, :survey_readiness_information_date, :installation_readiness_information_date,
              :pending_amount
json.brand do
  json.extract! survey.brand, :terms_and_conditions, :id, :name, :code
  json.logo survey.brand.brand_logo&.attachment&.url
end
json.booking do
  json.extract! survey.booking, :id, :ecb_booking_code
end
# json.brand_dealer do
#   json.extract! survey.brand_dealer, :id, :name, :code
# end
if survey.brand_model
  json.brand_model do
    json.extract! survey.brand_model, :id, :name, :code
  end
end
json.city do
  json.extract! survey.city, :id, :name, :code
end
if survey.zone
  json.zone do
    json.extract! survey.zone, :id, :name, :code
  end
end
if survey.state
  json.state do
    json.extract! survey.state, :id, :name, :code
  end
end
if survey.assigned_city_manager
  json.assigned_city_manager do
    json.extract! survey.assigned_city_manager, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if survey.assigned_field_agent1
  json.assigned_field_agent1 do
    json.extract! survey.assigned_field_agent1, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if survey.assigned_field_agent2
  json.assigned_field_agent2 do
    json.extract! survey.assigned_field_agent2, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if survey.assigned_partner_user
  json.assigned_partner_user do
    json.extract! survey.assigned_partner_user, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if survey.survey_partner
  json.survey_partner do
    json.extract! survey.survey_partner, :id, :name, :code
  end
end
if survey.approved_by
  json.approved_by do
    json.extract! survey.approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
