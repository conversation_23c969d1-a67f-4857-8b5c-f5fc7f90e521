json.extract! visit, :id,
              :dealer_name, :dealer_location, :model_variant, :model_colour, :model_description, :model_int_color,
              :model_ext_color, :customer_name, :contact_number,
              :alt_contact_number, :email, :booking_address, :address_line1, :address_line2, :locality,
              :pincode, :latitude, :longitude, :is_outstation, :company_name, :gst,
              :site_poc_name, :site_poc_contact, :ecb_booking_code, :ecb_visit_code,
              :customer_visit_confirmed, :status, :on_hold_reason, :cancel_reason,
              :cancelled_at, :first_contact_at, :customer_ok_with_recording, :remarks, :next_follow_up_at,
              :approved_at,
              :customer_billing, :created_at, :updated_at,
              :note_for_agent, :extra_cable_customer_pay,
              :scheduled_visit_date_time, :payment_remarks,
              :summary_stage1, :summary_stage2, :agent_notes, :charger_capacity, :charger_make_type, :cable_length, :cable_length_new,
              :sanctioned_load, :vdc, :flag, :mdi, :cable_gauge, :obc, :customer_city, :avg_load_utilized, :visit_partner_id
json.brand do
  json.extract! visit.brand, :terms_and_conditions, :id, :name, :code
  json.logo visit.brand.brand_logo&.attachment&.url
end
json.booking do
  json.extract! visit.booking, :id, :ecb_booking_code
end
# json.brand_dealer do
#   json.extract! visit.brand_dealer, :id, :name, :code
# end
if visit.brand_model
  json.brand_model do
    json.extract! visit.brand_model, :id, :name, :code
  end
end
json.city do
  json.extract! visit.city, :id, :name, :code
end
if visit.zone
  json.zone do
    json.extract! visit.zone, :id, :name, :code
  end
end
if visit.state
  json.state do
    json.extract! visit.state, :id, :name, :code
  end
end
if visit.assigned_city_manager
  json.assigned_city_manager do
    json.extract! visit.assigned_city_manager, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if visit.assigned_field_agent1
  json.assigned_field_agent1 do
    json.extract! visit.assigned_field_agent1, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if visit.assigned_field_agent2
  json.assigned_field_agent2 do
    json.extract! visit.assigned_field_agent2, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if visit.assigned_partner_user
  json.assigned_partner_user do
    json.extract! visit.assigned_partner_user, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if visit.visit_partner
  json.visit_partner do
    json.extract! visit.visit_partner, :id, :name, :code
  end
end
if visit.approved_by
  json.approved_by do
    json.extract! visit.approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
