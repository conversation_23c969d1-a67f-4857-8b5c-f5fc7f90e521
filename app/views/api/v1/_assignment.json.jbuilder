json.extract! assignment, :id, :type, :booking_id, :survey_id, :code, :complaint_id, :complaint_receive_date,
              :dealer_name, :dealer_location, :model_variant, :model_colour, :model_description, :model_int_color,
              :model_ext_color, :customer_name, :contact_number, :old_cable_used_length, :new_cable_used_length, :number_of_old_mcb_rcbo_used,
              :number_of_new_mcb_rcbo_used, :number_of_old_mcb_rcbo_box_used, :number_of_new_mcb_rcbo_box_used, :nature_of_install_remarks, :nature_of_work,
              :alt_contact_number, :email, :booking_address, :address_line1, :address_line2, :locality,
              :pincode, :latitude, :longitude, :is_outstation, :is_outstation_for_client, :company_name, :gst, :dealer_gst,
              :site_poc_name, :site_poc_contact, :ecb_booking_code, :ecb_survey_code,
              :brand_booking_code, :brand_booking_date, :ecb_receive_date, :brand_booking_status,
              :customer_survey_confirmed, :status, :on_hold_reason, :cancel_reason, :close_reason, :close_complaint_summary,
              :cancelled_at, :first_contact_at, :customer_ok_with_recording, :remarks, :next_follow_up_at,
              :approved_at, :survey_report_sent_at, :layout_approved_at, :is_no_show_during_survey, :is_extra_material_used,
              :customer_billing, :created_at, :updated_at, :is_no_show_during_installation,
              :note_for_agent, :extra_cable_customer_pay, :survey_completed_at, :install_completed_at,
              :scheduled_survey_date_time, :scheduled_visit_date_time, :actual_survey_date_time, :actual_visit_date_time, :visit_completed_at, :actual_install_date_time,
              :summary_stage1, :summary_stage2, :agent_notes, :charger_capacity, :charger_make_type, :cable_length, :cable_length_new,
              :sanctioned_load, :vdc, :flag, :mdi, :cable_gauge, :scheduled_install_date_time,
              :customer_install_confirmed, :charger_issued_date, :install_type, :survey_done_same_day,
              :vin, :commission_number, :engine_number, :ecb_install_code, :sendbird_channel, :customer_signed_at, :customer_rating, :ecb_visit_code,
              :customer_feedback, :obc, :customer_city, :avg_load_utilized, :visit_partner_id, :visit_partner, :brand_complaint_number, :ecb_complaint_code, :complaint_brand_model_name,
              :payment_received, :amount_to_receive, :amount_to_refund, :invoice_total, :complementary_cable_length, :pricing_per_meter, :invoice_date, :invoice_number, :invoice_amount,
              :base_amount, :gst_amount, :bhe, :bhe_date, :payment_count, :refund_count, :payment_remarks, :is_sow_completed, :seek_payment_email_preview, :refund_email_preview, :refund_processed_email_preview,
              :pricing_expired, :dealer_email, :cee_name, :cee_contact_date, :city_manager_email, :zonal_manager_email,
              :survey_final_video_link, :installation_final_video_link, :installation_clv_video_link,
              :seek_payment_email_sent, :refund_email_sent, :refund_processed_email_sent, :commission_date, :expected_delivery_date,
              :sow_status, :sow_completion_date, :extra_cable_payment_status, :extra_cable_payment_date, :survey_readiness_information_date, :installation_readiness_information_date, :invoice_remarks,
              :pending_amount, :customer_proposed_survey_date, :customer_proposed_survey_date_updated_at, :customer_proposed_install_date, :customer_proposed_install_date_updated_at
json.brand do
  json.extract! assignment.brand, :terms_and_conditions, :id, :name, :code, :add_to_forms
  json.logo assignment.brand.brand_logo&.attachment&.url
end
json.zip_file assignment.zip_file&.attachment&.url if assignment.zip_file&.attached?
json.customer_sign assignment.customer_sign&.attachment&.url if assignment.customer_sign&.attached?
json.ecb_sign assignment.ecb_sign&.attachment&.url if assignment.ecb_sign&.attached?
json.agent_sign assignment.agent_sign&.attachment&.url if assignment.agent_sign&.attached?
json.previous_visit_date_time assignment.previous_visit_date_time
# json.brand_dealer do
#   json.extract! assignment.brand_dealer, :id, :name, :code
# end
if assignment.brand_model
  json.brand_model do
    json.extract! assignment.brand_model, :id, :name, :code
  end
end
if assignment.booking_id
  json.booking do
    json.extract! assignment.booking, :id, :ecb_booking_code
  end
end
if assignment.survey_id
  json.survey do
    json.extract! assignment.survey, :id, :ecb_survey_code
  end
end
json.city do
  json.extract! assignment.city, :id, :name, :code
end
if assignment.zone
  json.zone do
    json.extract! assignment.zone, :id, :name, :code
  end
end
if assignment.state
  json.state do
    json.extract! assignment.state, :id, :name, :code
  end
end
if assignment.assigned_city_manager
  json.assigned_city_manager do
    json.extract! assignment.assigned_city_manager, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if assignment.assigned_field_agent1
  json.assigned_field_agent1 do
    json.extract! assignment.assigned_field_agent1, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if assignment.assigned_field_agent2
  json.assigned_field_agent2 do
    json.extract! assignment.assigned_field_agent2, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if assignment.assigned_partner_user
  json.assigned_partner_user do
    json.extract! assignment.assigned_partner_user, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if assignment.survey_partner
  json.survey_partner do
    json.extract! assignment.survey_partner, :id, :name, :code
  end
end
if assignment.install_partner
  json.install_partner do
    json.extract! assignment.install_partner, :id, :name, :code
  end
end
if assignment.approved_by
  json.approved_by do
    json.extract! assignment.approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if assignment.layout_approved_by
  json.layout_approved_by do
    json.extract! assignment.layout_approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
json.surveys assignment.surveys.select(:id, :ecb_survey_code, :status)
json.installations assignment.installations.select(:id, :ecb_install_code, :status)
