json.extract! installation, :id,
              :dealer_name, :dealer_location, :model_variant, :model_colour, :model_description, :model_int_color,
              :model_ext_color, :customer_name, :contact_number,
              :alt_contact_number, :email, :booking_address, :address_line1, :address_line2, :locality,
              :pincode, :latitude, :longitude, :is_outstation, :is_outstation_for_client, :company_name, :gst,
              :site_poc_name, :site_poc_contact, :ecb_booking_code,
              :status, :on_hold_reason, :cancel_reason,
              :cancelled_at, :first_contact_at, :customer_ok_with_recording, :remarks, :next_follow_up_at,
              :approved_at,
              :customer_billing, :created_at, :updated_at,
              :note_for_agent, :extra_cable_customer_pay,
              :summary_stage1, :summary_stage2, :agent_notes, :charger_capacity, :charger_make_type, :cable_length, :cable_length_new,
              :sanctioned_load, :vdc, :flag, :mdi, :cable_gauge,
              :scheduled_install_date_time, :customer_install_confirmed, :charger_issued_date,
              :install_type, :survey_done_same_day, :vin, :engine_number, :obc, :customer_city, :avg_load_utilized, :complementary_cable_length, :pricing_per_meter,
              :base_amount, :gst_amount, :invoice_amount :payment_count, :refund_count, :payment_remarks, :seek_payment_email_preview, :refund_email_preview, :refund_processed_email_preview,
              :pricing_expired, :cee_name, :cee_contact_date, :dealer_email, :city_manager_email, :zonal_manager_email, :survey_final_video_link, :installation_final_video_link, :installation_clv_video_link,
              :seek_payment_email_sent, :refund_email_sent, :refund_processed_email_sent, :commission_date,
              :sow_status, :sow_completion_date, :extra_cable_payment_status, :extra_cable_payment_date, :survey_readiness_information_date, :installation_readiness_information_date
json.brand do
  json.extract! installation.brand, :terms_and_conditions, :id, :name, :code
  json.logo installation.brand.brand_logo&.attachment&.url
end
json.booking do
  json.extract! installation.booking, :id, :ecb_booking_code
end
json.survey do
  json.extract! installation.survey, :id, :ecb_survey_code
end

# json.brand_dealer do
#   json.extract! installation.brand_dealer, :id, :name, :code
# end
if installation.brand_model
  json.brand_model do
    json.extract! installation.brand_model, :id, :name, :code
  end
end
json.city do
  json.extract! installation.city, :id, :name, :code
end
if installation.zone
  json.zone do
    json.extract! installation.zone, :id, :name, :code
  end
end
if installation.state
  json.state do
    json.extract! installation.state, :id, :name, :code
  end
end
if installation.assigned_city_manager
  json.assigned_city_manager do
    json.extract! installation.assigned_city_manager, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if installation.assigned_field_agent1
  json.assigned_field_agent1 do
    json.extract! installation.assigned_field_agent1, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if installation.assigned_field_agent2
  json.assigned_field_agent2 do
    json.extract! installation.assigned_field_agent2, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if installation.assigned_partner_user
  json.assigned_partner_user do
    json.extract! installation.assigned_partner_user, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if installation.install_partner
  json.install_partner do
    json.extract! installation.install_partner, :id, :name, :code
  end
end
if installation.approved_by
  json.approved_by do
    json.extract! installation.approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
