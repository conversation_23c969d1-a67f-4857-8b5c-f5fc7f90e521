json.extract! booking, :id, :brand_booking_code,
              :dealer_name, :dealer_location, :brand_booking_date, :model_variant, :model_colour, :model_description, :model_int_color,
              :model_ext_color, :ecb_receive_date, :customer_name, :contact_number,
              :alt_contact_number, :email, :booking_address, :address_line1, :address_line2, :locality,
              :pincode, :latitude, :longitude, :is_outstation, :is_outstation_for_client, :company_name, :gst, :dealer_gst,
              :site_poc_name, :site_poc_contact, :ecb_booking_code,
              :customer_survey_confirmed, :status, :on_hold_reason, :cancel_reason,
              :cancelled_at, :first_contact_at, :customer_ok_with_recording, :remarks, :next_follow_up_at,
              :approved_at, :payment_remarks,
              :customer_billing, :created_at, :updated_at, :dealer_email, :cee_name, :cee_contact_date, :city_manager_email, :zonal_manager_email,
              :survey_final_video_link, :installation_final_video_link, :installation_clv_video_link, :commission_date, :expected_delivery_date,
              :sow_status, :sow_completion_date, :extra_cable_payment_status, :extra_cable_payment_date, :survey_readiness_information_date, :installation_readiness_information_date,
              :customer_proposed_survey_date, :customer_proposed_survey_date_updated_at, :customer_proposed_install_date, :customer_proposed_install_date_updated_at
json.brand do
  json.extract! booking.brand, :terms_and_conditions, :id, :name, :code
  json.logo booking.brand.brand_logo&.attachment&.url
end
# json.brand_dealer do
#   json.extract! booking.brand_dealer, :id, :name, :code
# end
if booking.brand_model
  json.brand_model do
    json.extract! booking.brand_model, :id, :name, :code
  end
end
json.city do
  json.extract! booking.city, :id, :name, :code
end
if booking.zone
  json.zone do
    json.extract! booking.zone, :id, :name, :code
  end
end
if booking.state
  json.state do
    json.extract! booking.state, :id, :name, :code
  end
end
if booking.assigned_city_manager
  json.assigned_city_manager do
    json.extract! booking.assigned_city_manager, :id, :email, :name, :mobile, :alternate_mobile
  end
end
if booking.approved_by
  json.approved_by do
    json.extract! booking.approved_by, :id, :email, :name, :mobile, :alternate_mobile
  end
end
