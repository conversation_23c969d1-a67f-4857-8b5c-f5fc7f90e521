wb = xlsx_package.workbook

wb.add_worksheet(name: "Data") do |sheet|
  column_types = Array.new(81)
  # column_types[51]= :string

  # Create the header row
  sheet.add_row [
    "SN",
    "Brand Name",
    "ZON<PERSON>",
    "City Manager",
    "Dealer Name",
    "Dealer Location",
    "Customer Name",
    "Customer Booking Ref No",
    "Booking Date",
    "Brand booking status",
    "Survey Status as on #{Date.today.strftime('%d-%m-%Y')}",
    "Installation Status as on #{Date.today.strftime('%d-%m-%Y')}",
    "Booking Status as on #{Date.today.strftime('%d-%m-%Y')}",
    "Model Name",
    "Variant",
    "Colour",
    "Charger Type",
    "Customer Address 1",
    "Customer Address2",
    "Customer city of Survey/Installation",
    "EV city",
    "PIN Code",
    "State",
    "Customer Phone Number1",
    "Customer Phone Number2",
    "ECB Booking rcv date",
    "Booking Creation Date",
    "Survey Creation Date",
    "Installation Creation Date",
    "Survey Approval Date",
    "Installation Approval Date",
    "Customer Contact Logs",
    "Booking Outstation for Installer Y/N",
    "Survey Outstation for Installer Y/N",
    "Installation Outstation for Installer Y/N",
    "Booking Outstation for Client Y/N",
    "Survey Outstation for Client Y/N",
    "Installation Outstation for Client Y/N",
    "Confirmed for Survey (Y/N)",
    "Customer Ok with call recording [Y/N]",
    "Scheduled Date of survey",
    "Survey end Date",
    "Booking Ref Number",
    "Survey Ref Number",
    "Old Survey Number",
    "Survey Partner Name",
    "Broad Survey Findings Stage 1",
    "Broad Survey Findings Stage 2",
    "Comments",
    "Installation Ref Number",
    "Date of issue of charger by the dealer to the customer",
    "Make of Charger",
    "Scheduled Date of Installation",
    "Installation end Date",
    "Installer Partner Name ",
    "Customer Email ID",
    "Installation Type",
    "Charger Serial No.",
    "Attributes",
    "Next Follow up date",
    "cable length at survey",
    "cable length at installation",
    "Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation",
    "Survey and installation on same day [Y/N]",
    "No show date [ at survey stage ]",
    "No show comments",
    "No show date [installation stage]",
    "No show comments",
    "BHE",
    "BHE Date",
    "Installation addresss [if different from the customer address mentioned in Column L]",
    "Extra Switch gear/Material used at site provided by installer after ECB approval?",
    "Final Status",
    "VIN ",
    "Commission Number",
    "CFF:Q1: Overall satisfaction",
    "CFF:Q6: Any open issues",
    "Total Sanctioned Load",
    "Average Load Utilised",
    "Survey Customer Rating",
    "Installation Customer Rating",
    "Booking Uploaded By",
    "Type of power cable",
    "Core of power cable",
    "MCB used on site",
    "MCB box used on site",
    "Is this client (OEM) paid - Installation ?",
    "Is this client (OEM) paid - Survey ?",
    "Is there a no show during survey stage ?",
    "Is there a no show during installation stage ?",
    "Any extra material used at installation ? if yes then mention",
    "Surveyor Name",
    "First Contact Date",
    "Last Updated"
  ]

  # Create entries for each assignment
  counter = 1
  @bookings.each do |booking|
    sheet.add_row [
      counter,
      booking.brand.name,
      booking.zone&.name,
      booking.assigned_city_manager&.name,
      booking.dealer_name,
      booking.dealer_location,
      booking.customer_name,
      booking.brand_booking_code,
      booking.brand_booking_date&.to_ist_format,
      booking.brand_booking_status,
      '',
      '',
      booking.status,
      booking.brand_model&.name,
      booking.model_variant,
      booking.model_colour,
      booking.obc,
      booking.address_line1,
      booking.address_line2,
      booking.customer_city,
      booking.city&.name,
      booking.first_survey&.pincode || booking.pincode,
      booking.state&.name,
      booking.contact_number,
      booking.alt_contact_number,
      booking.ecb_receive_date&.to_ist_format,
      booking.created_at&.to_ist_format,
      '',
      '',
      '',
      '',
      booking.customer_connect_logs_remarks.sort.reverse.map(&:last).join(", "),
      booking.is_outstation ? 'Y' : booking.is_outstation.nil? ? '' : 'N',
      '',
      '',
      booking.is_outstation_for_client ? 'Y' : booking.is_outstation_for_client.nil? ? '' : 'N',
      '',
      '',
      '',
      booking.customer_ok_with_recording ? 'Y' : 'N',
      '',
      '',
      booking.ecb_booking_code,
      booking.ecb_survey_code,
      booking.old_survey_number,
      booking.survey_partner&.name,
      booking.summary_stage1,
      booking.summary_stage2,
      booking.customer_connect_logs_remarks.sort.last&.second&.capitalize&.gsub('_', ' '),
      '',
      '',
      '',
      '',
      '',
      '',
      booking.email,
      '',
      '',
      '',
      booking.future_next_follow_up_at&.to_ist_format,
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      booking.bhe ? 'Y' : booking.bhe.nil? ? '' : 'N',
      booking.bhe_date&.to_ist_format,
      '',
      '',
      '',
      '',
      booking.commission_number,
      '',
      '',
      '',
      booking.avg_load_utilized,
      '',
      '',
      booking.uploaded_by&.name,
      booking.type_of_power_cable,
      booking.core_of_power_cable,
      booking.mcb_used_on_site,
      booking.mcb_box_used_on_site,
      '',
      '',
      '',
      '',
      booking.first_installation&.is_extra_material_used ? "Y" : "N",
      '',
      booking.first_contact_at&.to_ist_format,
      booking.updated_at&.to_ist_format
    ], types: column_types
    counter = counter + 1
  end

  @surveys.each do |survey|
    sheet.add_row [
      counter,
      survey.booking.brand.name,
      survey.booking.zone&.name,
      survey.booking.assigned_city_manager&.name,
      survey.booking.dealer_name,
      survey.booking.dealer_location,
      survey.booking.customer_name,
      survey.booking.brand_booking_code,
      survey.booking.brand_booking_date&.to_ist_format,
      survey.booking.brand_booking_status,
      survey.status,
      '',
      survey.booking.status,
      survey.booking.brand_model&.name,
      survey.booking.model_variant,
      survey.booking.model_colour,
      survey.booking.obc,
      survey.address_line1,
      survey.address_line2,
      survey.customer_city,
      survey.booking.city&.name,
      survey.pincode,
      survey.booking.state&.name,
      survey.booking.contact_number,
      survey.booking.alt_contact_number,
      survey.booking.ecb_receive_date&.to_ist_format,
      survey.booking&.created_at&.to_ist_format,
      survey&.created_at&.to_ist_format,
      '',
      survey&.approved_at&.to_ist_format,
      '',
      (survey.booking.customer_connect_logs_remarks + survey.customer_connect_logs_remarks ).sort.reverse.map(&:last).join(", "),
      survey.booking.is_outstation ? 'Y' : survey.booking.is_outstation.nil? ? '' : 'N',
      survey.is_outstation ? 'Y' : survey.is_outstation.nil? ? '' : 'N',
      '',
      survey.booking.is_outstation_for_client ? 'Y' : survey.booking.is_outstation_for_client.nil? ? '' : 'N',
      survey.is_outstation_for_client ? 'Y' : survey.is_outstation_for_client.nil? ? '' : 'N',
      '',
      survey.customer_survey_confirmed || survey.booking.customer_survey_confirmed ? 'Y' : 'N',
      survey.booking.customer_ok_with_recording ? 'Y' : 'N',
      survey.scheduled_survey_date_time&.to_ist_format,
      survey.survey_completed_at&.to_ist_format,
      survey.booking.ecb_booking_code,
      survey.ecb_survey_code,
      survey.old_survey_number,
      survey.survey_partner&.name,
      survey.summary_stage1,
      survey.summary_stage2,
      survey.customer_connect_logs_remarks.sort.last&.second&.capitalize&.gsub('_', ' '),
      '',
      '',
      '',
      '',
      '',
      '',
      survey.booking.email,
      '',
      '',
      '',
      survey.future_next_follow_up_at&.to_ist_format,
      survey.cable_length_new || survey.cable_length,
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      survey.booking.bhe ? 'Y' : survey.booking.bhe.nil? ? '' : 'N',
      survey.booking.bhe_date&.to_ist_format,
      '',
      '',
      '',
      '',
      survey.commission_number,
      '',
      '',
      '',
      survey.avg_load_utilized,
      survey&.customer_rating,
      '',
      survey.booking.uploaded_by&.name,
      survey.type_of_power_cable,
      survey.core_of_power_cable,
      survey.mcb_used_on_site,
      survey.mcb_box_used_on_site,
      survey.first_installation&.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
      survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
      survey.is_no_show_during_survey ? 'Y' : 'N',
      '',
      survey.first_installation&.is_extra_material_used ? "Y" : "N",
      survey.surveyor_name,
      [survey.first_contact_at, survey.booking.first_contact_at].compact.min&.to_ist_format,
      survey.updated_at&.to_ist_format
    ], types: column_types
    counter = counter + 1
  end

  @installations.each do |installation|
    sheet.add_row [
      counter,
      installation.booking.brand.name,
      installation.booking.zone&.name,
      installation.booking.assigned_city_manager&.name,
      installation.booking.dealer_name,
      installation.booking.dealer_location,
      installation.booking.customer_name,
      installation.booking.brand_booking_code,
      installation.booking.brand_booking_date&.to_ist_format,
      installation.booking.brand_booking_status,
      installation.survey.status,
      installation.status,
      installation.booking.status,
      installation.booking.brand_model&.name,
      installation.booking.model_variant,
      installation.booking.model_colour,
      installation.booking.obc,
      installation.survey.address_line1,
      installation.survey.address_line2,
      installation.customer_city,
      installation.booking.city&.name,
      installation.survey.pincode,
      installation.booking.state&.name,
      installation.booking.contact_number,
      installation.booking.alt_contact_number,
      installation.booking.ecb_receive_date&.to_ist_format,
      installation.booking&.created_at&.to_ist_format,
      installation.survey&.created_at&.to_ist_format,
      installation&.created_at&.to_ist_format,
      installation&.survey&.approved_at&.to_ist_format,
      installation&.approved_at&.to_ist_format,
      (installation.booking.customer_connect_logs_remarks + installation.survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks ).sort.reverse.map(&:last).join(", "),
      installation.booking.is_outstation ? 'Y' : installation.booking.is_outstation.nil? ? '' : 'N',
      installation.survey.is_outstation ? 'Y' : installation.survey.is_outstation.nil? ? '' : 'N',
      installation.is_outstation ? 'Y' : installation.is_outstation.nil? ? '' : 'N',
      installation.booking.is_outstation_for_client ? 'Y' : installation.booking.is_outstation_for_client.nil? ? '' : 'N',
      installation.survey.is_outstation_for_client ? 'Y' : installation.survey.is_outstation_for_client.nil? ? '' : 'N',
      installation.is_outstation_for_client ? 'Y' : installation.is_outstation_for_client.nil? ? '' : 'N',
      installation.survey.customer_survey_confirmed || installation.booking.customer_survey_confirmed ? 'Y' : 'N',
      installation.booking.customer_ok_with_recording ? 'Y' : 'N',
      installation.survey.scheduled_survey_date_time&.to_ist_format,
      installation.survey.survey_completed_at&.to_ist_format,
      installation.booking.ecb_booking_code,
      installation.survey.ecb_survey_code,
      installation.survey.old_survey_number,
      installation.survey.survey_partner&.name,
      installation.survey.summary_stage1,
      installation.survey.summary_stage2,
      installation.customer_connect_logs_remarks.sort.last&.second&.capitalize&.gsub('_', ' '),
      installation.ecb_install_code,
      installation.charger_issued_date,
      installation.charger_make_type,
      installation.scheduled_install_date_time&.to_ist_format,
      installation.install_completed_at&.to_ist_format,
      installation.install_partner&.name,
      installation.booking.email,
      installation.install_type_string,
      installation.booking.first_installation&.charger_serial_number ? " #{installation.booking.first_installation&.charger_serial_number}" : '',
      '',
      installation.future_next_follow_up_at&.to_ist_format,
      installation.survey.cable_length_new || installation.survey.cable_length,
      installation.status_cancelled? ? '' : installation.cable_length_new || installation.cable_length,
      installation.cable_gauge,
      installation.install_completed_at && installation.install_completed_at.to_ist_format.eql?(installation.survey.survey_completed_at&.to_ist_format) ? 'Y' : 'N',
      '',
      '',
      '',
      '',
      installation.booking.bhe ? 'Y' : installation.booking.bhe.nil? ? '' : 'N',
      installation.booking.bhe_date&.to_ist_format,
      '',
      '',
      '',
      installation.vin,
      installation.commission_number,
      '',
      '',
      installation.sanctioned_load,
      installation.avg_load_utilized,
      '',
      installation&.customer_rating,
      installation.booking.uploaded_by&.name,
      installation.type_of_power_cable,
      installation.core_of_power_cable,
      installation.mcb_used_on_site,
      installation.mcb_box_used_on_site,
      installation.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
      installation.survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
      installation.survey.is_no_show_during_survey ? 'Y' : 'N',
      installation.is_no_show_during_installation ? 'Y' : 'N',
      installation.is_extra_material_used ? "Y" : "N",
      installation.surveyor_name,
      [installation.first_contact_at, installation.booking.first_contact_at, installation.survey.first_contact_at].compact.min&.to_ist_format,
      installation.updated_at&.to_ist_format
    ], types: column_types
    counter = counter + 1
  end
end

