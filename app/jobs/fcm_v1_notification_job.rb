require 'fcmpush'
require 'json'

class FcmV1NotificationJob < ApplicationJob
  queue_as :default

  def perform(registration_ids, payload)
    Rails.logger.info('Sending all pending notifications...')
    registration_ids.reject! { |id| id.nil? || id.empty? || id == '' }
    unique_registration_ids = registration_ids.uniq
    return if unique_registration_ids.empty?

    extra_data = payload[:meta]
    extra_data.each_key do |key|
      extra_data[key] = extra_data[key].to_s
    end

    project_id = 'ecb-prod'
    client = Fcmpush.new(project_id)
    extra_data[:title] = payload[:title]
    extra_data[:body] = payload[:body]
    payloads = unique_registration_ids.map do |token|
      {
        message: {
          token:,
          data: extra_data
        }
      }
    end

    # response = client.batch_push(payloads)
    # response_array = response.json

    response_array = []
    payloads.each do |payload_single|
      response = client.push(payload_single)
      response_array << response.json
    rescue Fcmpush::ClientError => e
      response_array << JSON.parse(e.response.body)
    end

    Rails.logger.info("Sent all pending notifications... with response: #{response_array}")

    ### TODO: Replacement and deletion of registration ids needs to be implemented

    # canonical_ids = response_raw[:canonical_ids]
    # canonical_ids&.each do |result|
    #   UserFcmRegistration.where(fcm_registration_token: result[:old]).update_all(fcm_registration_token: result[:new])
    #   Rails.logger.info("Replaced registration Id with response: #{result[:old]} with: #{result[:new]}")
    # end

    response_array.each_with_index do |result, index|
      if result['error'].present?
        if result['error']['status'] == 'NOT_FOUND'
          UserFcmRegistration.where(fcm_registration_token: unique_registration_ids[index]).delete_all
          Rails.logger.info("Removed single registration Id: #{unique_registration_ids[index]}")
        else
          Rails.logger.error("Error sending single notification to: #{unique_registration_ids[index]} with error: #{result['error']}")
        end
      end
    end
  end
end
