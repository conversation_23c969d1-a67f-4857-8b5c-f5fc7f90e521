class PaymentTrackerReportGenerateJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info("Payment tracker report generation on #{Date.today.strftime('%d-%m-%Y')} started..")
    index = 0
    xlsx_package = Axlsx::Package.new
    Report.new('sidekiq', filters).new_payment_tracker_assignments.in_batches(of: 1000) do |assignments|
      assignments = assignments.with_payment_tracker_report_association
      index = ExcelDataGenerator::NewPaymentTrackerReport.new(assignments, xlsx_package, !index.zero?, index).generate
    end

    # excel_data = xlsx_package.to_stream.read
    
    temp_file = Tempfile.new(['payment_tracker', '.xlsx'])
    xlsx_package.serialize(temp_file.path)
    Rails.logger.info("Payment tracker report generation on #{Date.today.strftime('%d-%m-%Y')} done!")

    Rails.logger.info("Payment tracker report generation on #{Date.today.strftime('%d-%m-%Y')} uploading to s3....")
    upload_to_s3(temp_file)
    Rails.logger.info("Payment tracker report generation on #{Date.today.strftime('%d-%m-%Y')} uploading to done!")

    # Ensure the temporary file is deleted
    temp_file.close
    File.delete(temp_file.path) if File.exist?(temp_file.path)
  end

  def filters
    {
      start_date: "2023-01-01",
      end_date: Date.today
    }
  end

  def upload_to_s3(temp_file)
    filename = "payment_tracker.xlsx"
    file_key = "reports/#{filename}"
    
    obj = AWS_S3_BUCKET.object(file_key)
    obj.upload_file(temp_file.path)

    obj
  end
end
