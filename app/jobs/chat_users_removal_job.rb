class ChatUsersRemovalJob < ApplicationJob
  queue_as :default

  def perform
    date = Date.today - (ENV['SENDBIRD_GROUP_VALIDITY'] || 7).to_i.days
    from = date.beginning_of_day
    to = date.end_of_day
    # bookings = Booking.valid_expired_sendbird_bookings(from, to)
    # bookings.each do |booking|
    #   user_ids = User.where(id: booking.users_for_chat).pluck(:mobile).compact
    #   booking.remove_users_from_chat(user_ids)
    # end
  end
end
