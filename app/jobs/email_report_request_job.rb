class EmailReportRequestJob < ApplicationJob
  queue_as :default

  def perform(user, report_type, filters)
    Rails.logger.info("Report #{report_type} generation started......")
    index = 0
    xlsx_package = Axlsx::Package.new 

    case report_type
    when "database"
      Report.new(user, filters).database_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_database_report_association
        index = ExcelDataGenerator::DatabaseReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "database_two"
      Report.new(user, filters).database_two_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_database_report_association
        index = ExcelDataGenerator::DatabaseTwoReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "database_three"
      Report.new(user, filters).database_three_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_database_report_association
        index = ExcelDataGenerator::DatabaseThreeReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "database_recent_new"
      Report.new(user, filters).database_recent_new_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_database_report_association
        index = ExcelDataGenerator::DatabaseReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "client_database"
      Report.new(user, filters).client_database_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_database_report_association
        index = ExcelDataGenerator::ClientDatabaseReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "installations"
      Report.new(user, filters).installations_assignments.in_batches(of: 1000) do |assignments|
        index = ExcelDataGenerator::InstallationsReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "invoicing_installation"
      Report.new(user, filters).invoicing_installation_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_invoicing_installation_report_association
        index = ExcelDataGenerator::InvoicingInstallationReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "invoicing_survey"
      Report.new(user, filters).invoicing_survey_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_invoicing_survey_report_association
        index = ExcelDataGenerator::InvoicingSurveyReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "payment_tracker"
      Report.new(user, filters).new_payment_tracker_assignments.in_batches(of: 1000) do |assignments|
        assignments = assignments.with_payment_tracker_report_association
        index = ExcelDataGenerator::NewPaymentTrackerReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    when "complaints_database"
      Report.new(user, filters).complaints_database_assignments.in_batches(of: 1000) do |assignments|
        index = ExcelDataGenerator::ComplaintDatabaseReport.new(assignments, xlsx_package, !index.zero?, index).generate
      end
    end
    temp_file = Tempfile.new([report_type, '.xlsx'])
    xlsx_package.serialize(temp_file.path)
    Rails.logger.info("Report #{report_type} generation completed......")
    ReportsMailer.report_generate_success(user, Report::REPORT_TYPES[report_type], filters[:start_date], filters[:end_date], temp_file).deliver_now
  rescue => e
    ReportsMailer.report_generate_failed(user, Report::REPORT_TYPES[report_type], filters[:start_date], filters[:end_date]).deliver_now
  end
end
