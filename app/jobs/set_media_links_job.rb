class SetMediaLinksJob < ApplicationJob
  queue_as :default

  def perform(assignment)
    linked_fields = ['installation_clv_video_link', 'survey_final_video_link', 'installation_final_video_link']
    f_subs = assignment.form_submissions.includes(:form_question).where(form_question: { linked_field: linked_fields })
    update_hash = {}
    f_subs.each do |form_submission|
      video_url = form_submission.videos.last&.url
      if video_url.present?
        case form_submission.form_question.linked_field
        when 'survey_final_video_link'
          update_hash[:survey_final_video_link] = video_url
        when 'installation_final_video_link'
          update_hash[:installation_final_video_link] = video_url
        when 'installation_clv_video_link'
          update_hash[:installation_clv_video_link] = video_url
        end
      end
    end
    assignment.update_columns(update_hash) if update_hash.present?
  end
end
