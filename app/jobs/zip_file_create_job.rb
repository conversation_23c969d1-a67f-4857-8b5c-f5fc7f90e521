class ZipFileCreateJob < ApplicationJob
  queue_as :default

  def perform(assignment_id)
    assignment = Booking.find(assignment_id)

    #---------- Download files from S3 -------------------------------- #
    # Tmp folder to store the download files from S3
    tmp_folder = "tmp/archive_#{assignment.code}_#{Time.now.to_i}"
    # Create a tmp folder if not exists
    FileUtils.mkdir_p(tmp_folder) unless Dir.exist?(tmp_folder)

    # download files from S3
    if assignment.customer_sign.present?
      download_file_from_s3(assignment.customer_sign, tmp_folder,
                            'customer_sign.jpg')
    end
    if assignment.ecb_sign.present?
      download_file_from_s3(assignment.ecb_sign, tmp_folder,
                            'ecb_sign.jpg')
    end
    download_file_from_s3(assignment.agent_sign, tmp_folder, 'agent_sign.jpg') if assignment.agent_sign.present?

    assignment.form_submissions.each do |form_submission|
      title = form_submission.form_question.title[0, 20].gsub(/[^0-9a-z]/i, '')
      counter = 1
      form_submission.images.each do |attachment|
        filename = "#{title}_#{counter}#{File.extname(attachment.blob.filename.to_s)}"
        download_file_from_s3(attachment, tmp_folder, filename)
        counter += 1
      end
      form_submission.videos.each do |attachment|
        filename = "#{title}_#{counter}#{File.extname(attachment.blob.filename.to_s)}"
        download_file_from_s3(attachment, tmp_folder, filename)
        counter += 1
      end
    end

    #---------- Convert to .zip --------------------------------------- #
    zip_filename = "#{tmp_folder}.zip"
    create_zip_from_tmp_folder(tmp_folder, zip_filename)

    # upoad zip file to S3
    assignment.skip_validations_callback = true
    assignment.zip_file.attach(io: File.open(zip_filename), filename: "#{assignment.code}.zip")
    upload_zip_to_s3_download_folder(zip_filename, assignment) if assignment.status_completed?

    # Remove files after S3 upload
    FileUtils.rm_rf([tmp_folder, zip_filename])
  end

  private

  def download_file_from_s3(attachment, tmp_folder, filename)
    File.open(File.join(tmp_folder, filename), 'wb') do |file|
      attachment.download { |chunk| file.write(chunk) }
    end
  end

  def create_zip_from_tmp_folder(tmp_folder, filename)
    Zip::File.open(filename, Zip::File::CREATE) do |zip_file|
      Dir[File.join(tmp_folder, '**', '**')].each do |file|
        zip_file.add(file.sub("#{tmp_folder}/", ''), file)
      end
    end
  end

  def upload_zip_to_s3_download_folder(zip_filename, assignment)
    date_str = (assignment.completed_at || DateTime.now).to_ist_format.strftime('%b%y/%d') # e.g., "Aug25/21"
    brand_code = assignment.brand.code # e.g., "MG"
    code = assignment.code # e.g., "I-MGCT24FEBKLP1"
    s3_key = "#{date_str}/#{brand_code}/#{code}.zip"

    obj = AWS_S3_DOWNLOAD_BUCKET.object(s3_key)

    File.open(zip_filename) do |file|
      obj.put(
        body: file,
        content_type: "application/zip"
      )
    end
  rescue => e
    Rails.logger.debug e
  end
end
