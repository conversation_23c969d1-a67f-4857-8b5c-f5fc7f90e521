require 'fcm'
require 'json'

class FcmNotificationJob < ApplicationJob
  queue_as :default

  def perform(registration_ids, payload)
    Rails.logger.info('Sending all pending notifications...')
    fcm_client = FCM.new(Rails.application.credentials[Rails.env.to_sym][:fcm_key]) # set your FCM_SERVER_KEY
    registration_ids.reject! { |id| id.nil? || id.empty? || id == '' }
    unique_registration_ids = registration_ids.uniq
    return if unique_registration_ids.empty?

    options = { priority: 'high', data: payload }

    response_raw = fcm_client.send(unique_registration_ids, options)
    response = JSON.parse(response_raw[:body])
    Rails.logger.info("Sent all pending notifications... with response: #{response}")
    canonical_ids = response_raw[:canonical_ids]
    canonical_ids&.each do |result|
      UserFcmRegistration.where(fcm_registration_token: result[:old]).update_all(fcm_registration_token: result[:new])
      Rails.logger.info("Replaced registration Id with response: #{result[:old]} with: #{result[:new]}")
    end

    not_registered_ids = response_raw[:not_registered_ids]
    return if not_registered_ids.nil?

    not_registered_ids.each do |result|
      UserFcmRegistration.where(fcm_registration_token: result).delete_all
      Rails.logger.info("Removed registration Id with response: #{result}")
    end
  end
end
