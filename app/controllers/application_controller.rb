class ApplicationController < ActionController::API
  include ActionController::MimeResponds
  include DeviseTokenAuth::Concerns::SetUserByToken
  before_action :configure_permitted_parameters, if: :devise_controller?
  respond_to :json
  rescue_from ActiveRecord::RecordNotFound, with: :render_not_found_response
  rescue_from ActiveRecord::RecordInvalid, with: :render_unprocessable_entity_response
  rescue_from ArgumentError, with: :render_invalid_response

  protected

  def update_auth_header
    Rails.logger.info "[AUTH DEBUG] in update_auth_header"
    # cannot save object if model has invalid params
    return unless @resource && @token.client

    Rails.logger.info "[AUTH DEBUG] Resource: #{@resource.id}"

    # Generate new client with existing authentication
    @token.client = nil unless @used_auth_by_token

    Rails.logger.info "[AUTH DEBUG] Token client: #{@token.client}"

    if @used_auth_by_token && !DeviseTokenAuth.change_headers_on_each_request

      Rails.logger.info "[AUTH DEBUG] Not changing headers on each request"

      # should not append auth header if @resource related token was
      # cleared by sign out in the meantime
      return if @resource.reload.tokens[@token.client].nil?

      Rails.logger.info "[AUTH DEBUG] Token data: #{@resource.tokens[@token.client]}"

      auth_header = @resource.build_auth_headers(@token.token, @token.client)

      Rails.logger.info "[AUTH DEBUG] Auth header built: #{auth_header.to_h}"

      # update the response header
      response.headers.merge!(auth_header)

      Rails.logger.info "[AUTH DEBUG] Response headers after merge: #{response.headers.to_h}"

      # set a server cookie if configured
      if DeviseTokenAuth.cookie_enabled
        set_cookie(auth_header)
      end
    else
      Rails.logger.info "[AUTH DEBUG] Changing headers on each request"

      unless @resource.reload.valid?
        @resource = @resource.class.find(@resource.to_param) # errors remain after reload
        # if we left the model in a bad state, something is wrong in our app
        unless @resource.valid?
          raise DeviseTokenAuth::Errors::InvalidModel, "Cannot set auth token in invalid model. Errors: #{@resource.errors.full_messages}"
        end
      end
      refresh_headers
    end
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_in, keys: %i[login password])
  end

  def authenticate_logged_in_user
    if current_user&.status_active?
      authenticate_api_v1_user!
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  def require_power_user
    return if current_user&.power_user?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_auth_user
    return if current_user&.auth_user?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_editor_user
    return if current_user&.client?
    return if current_user&.editor_user?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_field_agent
    return if current_user&.field_agent?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_editor_or_field_agent
    return if current_user&.editor_user? || current_user&.field_agent?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_import_user
    return if current_user&.import_user?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_finance_user
    return if current_user&.finance_user?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def require_mis_user
    return if current_user&.mis_user?

    render json: { error: 'Unauthorized' }, status: :unauthorized
  end

  def current_user
    current_api_v1_user
  end

  def include_all?
    params[:include_all].present? && params[:include_all].to_s.casecmp('true').zero?
  end

  private

  def render_not_found_response
    render json: { error: 'Object Not Found' }, status: :not_found
  end

  def render_unprocessable_entity_response(invalid)
    render json: { errors: invalid.record.errors.full_messages }, status: :unprocessable_entity
  end

  def render_invalid_response(exception)
    render json: { errors: exception.message }, status: :unprocessable_entity
  end
end
