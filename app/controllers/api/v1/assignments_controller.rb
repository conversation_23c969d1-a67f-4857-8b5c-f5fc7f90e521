module Api
  module V1
    class AssignmentsController < ApplicationController
      include <PERSON>Helper

      before_action :authenticate_logged_in_user
      before_action :require_auth_user
      before_action :require_editor_user,
                    except: %i[index show add_customer_connect_log start_work add_notes end_work customer_update
                               response_submit form_sections form_questions add_attachment remove_attachment
                               send_for_approval customer_connect_logs]
      before_action :require_field_agent, only: %i[start_work add_notes send_for_approval]
      before_action :require_editor_or_field_agent, only: %i[end_work]
      before_action :set_assignment, except: %i[index create import import_booking_status import_invoices transfer_bookings import_call_logs import_payment_details]

      def download_zip
        if @assignment.zip_file&.attached?
          redirect_to rails_blob_path(@assignment.zip_file, disposition: 'attachment',
                                                            filename: "#{@assignment.code}.zip",
                                                            type: 'application/zip')
          # redirect_to(@assignment.zip_file&.attachment&.url, allow_other_host: true)
        else
          zip_file = @assignment.process_and_create_zip_file
          # Sends the zip file to be download to the client's browser
          send_file(Rails.root.join(zip_file), type: 'application/zip', filename: "#{@assignment.code}.zip",
                                               disposition: 'attachment')
        end
      end

      def form_sections
        if @assignment.type == 'Survey'
          @form_sections = if @assignment.brand_model_id.present? && @assignment.brand_model.form_sections.survey.active.present?
                             @assignment.brand_model.form_sections.survey.active
                           else
                             @assignment.brand.form_sections.survey.active
                           end
        elsif @assignment.type == 'Installation'
          if @assignment.brand_model_id.present? && @assignment.brand_model.form_sections.installation.active.present?
            @form_sections = @assignment.brand_model.form_sections.installation.active
          else
            @form_sections = @assignment.brand.form_sections.installation.active
          end
        elsif @assignment.type == 'Visit'
          if @assignment.brand_model_id.present? && @assignment.brand_model.form_sections.visit.active.present?
            @form_sections = @assignment.brand_model.form_sections.visit.active
          else
            @form_sections = @assignment.brand.form_sections.visit.active
          end
        end
        @form_questions = {}
        @form_responses = {}
        @form_sections = @form_sections.includes(form_section_brands: [:brand, :brand_model])
        @form_sections.each do |form_section|
          @form_questions[form_section.id] = get_form_questions(form_section.id)
          form_response = get_form_responses(@form_questions[form_section.id].pluck(:form_question_id))
          @form_responses.merge!(form_response)
        end
        render :form_sections, status: :ok
      end

      def get_form_responses(question_ids)
        form_responses = {}
        form_submissions = @assignment.form_submissions.where(form_question_id: question_ids)
        form_submissions.each do |form_submission|
          form_responses[form_submission.form_question_id] = form_submission
        end
        form_responses
      end

      def get_form_questions(section_id)
        if @assignment.brand_model_id.present? &&
           @assignment.brand_model.form_questions.for_section(section_id).active.present?
          @assignment.brand_model.form_questions.for_section(section_id).active
        else
          @assignment.brand.form_questions.for_section(section_id).active
        end
      end

      def form_questions
        section_id = params[:section_id]
        if section_id.blank?
          render json: { error: 'Section not provided' }, status: :unprocessable_entity
          return false
        end
        @form_questions = get_form_questions(section_id)
        @form_responses = get_form_responses(@form_questions.pluck(:form_question_id))
        render :form_questions, status: :ok
      end

      def response_submit
        @response = @assignment.form_submissions.find_or_initialize_by(form_question_id: response_params[:form_question_id])
        @response.assign_attributes(response_params)
        if @response.save
          render :form_response, status: :ok
        else
          render json: @response.errors, status: :unprocessable_entity
        end
      end

      def approve
        @assignment.status = :in_progress
        @assignment.approved_at = Time.now
        @assignment.approved_by = current_api_v1_user
        if @assignment.save!
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def send_for_approval
        @assignment.status = :pending_approval
        if @assignment.save!
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def add_attachment
        @response = @assignment.form_submissions.find_by(form_question_id: response_params[:form_question_id])
        if response_params[:images].present?
          response_params[:images].each do |image|
            @response.images.attach(image)
          end
        end
        if response_params[:videos].present?
          response_params[:videos].each do |video|
            @response.videos.attach(video)
          end
        end
        if @response.save
          render :form_response, status: :ok
        else
          render json: @response.errors, status: :unprocessable_entity
        end

        # var files = document.getElementById('images').files.length;
        #   for (var x = 0; x < files; x++) {
        #       formData.append("response[images][]", document.getElementById('images').files[x]);
        #   }
        #   var files = document.getElementById('videos').files.length;
        #   for (var x = 0; x < files; x++) {
        #       formData.append("response[videos][]", document.getElementById('videos').files[x]);
        #   }
      end

      def remove_attachment
        @response = @assignment.form_submissions.find_by(form_question_id: response_params[:form_question_id])
        attachment = ActiveStorage::Attachment.find_by(record_id: @response.id,
                                                       record_type: 'FormSubmission', id: response_params[:attachment_id])
        if @response && attachment
          attachment.purge
          @response.reload
          render :form_response, status: :ok
        else
          render json: { error: 'Unable to process' }, status: :unprocessable_entity
        end
      end

      def update
        params = @assignment.type == 'Survey' ? survey_update_params : install_update_params
        if @assignment.update(params)
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def show; end

      def customer_update
        if @assignment.update(customer_update_params.merge(customer_signed_at: Time.now))
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def add_notes
        @assignment.agent_notes = @assignment.agent_notes ? "#{@assignment.agent_notes}, #{params[:notes]}" : params[:notes]
        if @assignment.save!
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def start_work
        field = @assignment.type == 'Survey' ? 'actual_survey_date_time' : @assignment.type == 'Visit' ? 'actual_visit_date_time' : 'actual_install_date_time'
        @assignment.send("#{field}=", Time.now)
        @assignment.status = :in_progress
        if @assignment.save!
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def end_work
        field = @assignment.type == 'Survey' ? 'survey_completed_at' : @assignment.type == 'Visit' ? 'visit_completed_at' : 'install_completed_at'
        @assignment.send("#{field}=", Time.now)
        @assignment.status = :completed
        if @assignment.save!
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def cancel
        if @assignment.update(cancelled_at: Time.now, cancel_reason: params[:reason], status: :cancelled)
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def on_hold
        if @assignment.update(on_hold_reason: params[:reason], status: :on_hold)
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def close
        if @assignment.update(complain_close_params.merge(status: :closed))
          render :show, status: :ok
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def add_customer_connect_log
        call_log = @assignment.customer_connect_logs.new(call_log_params.merge(user_id: current_api_v1_user.id))
        if call_log.save
          time_now = Time.now
          @assignment.update_column(:updated_at, time_now)
          @assignment.update_column(:first_contact_at, time_now) if @assignment.first_contact_at.blank?
          if call_log.next_follow_up_at.present?
            @assignment.update_column(:next_follow_up_at,
                                      call_log.next_follow_up_at)
          end
          render :show, status: :ok
        else
          render json: call_log.errors, status: :unprocessable_entity
        end
      end

      def edit_customer_connect_log
        call_log = @assignment.customer_connect_logs.find(params[:call_log][:id])
        if call_log.update(call_log_params.merge(user_id: current_api_v1_user.id))
          time_now = Time.now
          @assignment.update_column(:updated_at, time_now)
          @assignment.update_column(:first_contact_at, time_now) if @assignment.first_contact_at.blank?
          if call_log.next_follow_up_at.present?
            @assignment.update_column(:next_follow_up_at,
                                      call_log.next_follow_up_at)
          end
          render :show, status: :ok
        else
          render json: call_log.errors, status: :unprocessable_entity
        end
      end

      def index
        set_assignments
        @assignments = @assignments.where(type: %w[Survey Installation Visit])
        @assignments = @assignments.page(params[:page]).per(params[:page_size] || 20)
      end

      def customer_connect_logs
        @customer_connect_logs = @assignment.customer_connect_logs.order(created_at: :desc)
      end

      def create_payment
        @payment = @assignment.payments.new(payment_params)
        if @payment.save
          render :show, status: :created
        else
          render json: @payment.errors, status: :unprocessable_entity
        end
      end

      def edit_payment
        payment = @assignment.payments.find(params[:payment][:payment_id])
        if payment.update(payment_params)
          render :show, status: :created
        else
          render json: payment.errors, status: :unprocessable_entity
        end
      end

      def delete_payment
        payment =  @assignment.payments.find_by(id: params[:paymentId]);
        render json: { error: "Payment not found"}, status: :not_found unless payment.present?

        if payment&.destroy
          render json: { message: "Payment details deleted successfully" }, status: :ok
        else
          render json: { error: "Payment details not deleted successfully" }, status: :unprocessable_entity
        end
      end

      def delete_refund
        refund =  @assignment.refunds.find_by(id: params[:refundId]);
        render json: { error: "Refund not found"}, status: :not_found  unless refund.present?

        if refund&.destroy
          render json: { message: "Refund details deleted successfully" }, status: :ok
        else
          render json: { error: "Refund deletion operation failed" }, status: :unprocessable_entity
        end
      end

      def edit_refund
        refund = @assignment.refunds.find(params[:payment][:refund_id])
        if refund.update(payment_params)
          render :show, status: :created
        else
          render json: refund.errors, status: :unprocessable_entity
        end
      end

      def create_refund
        @refund = @assignment.refunds.new(payment_params)
        if @refund.save
          render :show, status: :created
        else
          render json: @refund.errors, status: :unprocessable_entity
        end
      end

      def payments
        return unless @assignment.present?
        @payments = @assignment.payments.order(created_at: :desc)
      end

      def refunds
        return unless @assignment.present?

        @refunds = @assignment.refunds.order(created_at: :desc)
      end

      private

      def set_assignment
        @assignment = if current_api_v1_user.mis_user?
                        Booking.find(params[:id])
                      elsif current_api_v1_user.client?
                        Booking.where(brand_id: current_api_v1_user.brand_id, id: params[:id]).first
                      elsif current_api_v1_user.finance_controller?
                        Booking.completed.where(type: ["Survey", "Installation"], id: params[:id]).first
                      else
                        Booking.where(
                          "id = ? AND (zone_id IN (?) OR assigned_city_manager_id = ? OR assigned_partner_user_id = ? OR assigned_field_agent1_id = ? OR assigned_field_agent2_id = ?)",
                          params[:id],
                          current_api_v1_user.zonal_manager? ? current_api_v1_user.get_zones_for_zonal_manager.pluck(:id) : [],
                          current_api_v1_user.city_manager? ? current_api_v1_user.id : nil,
                          current_api_v1_user.partner_manager? ? current_api_v1_user.id : nil,
                          current_api_v1_user.field_agent? ? current_api_v1_user.id : nil,
                          current_api_v1_user.field_agent? ? current_api_v1_user.id : nil
                        ).first
                       end
      end

      def set_assignments
        @assignments = if current_api_v1_user.mis_user?
                         Booking.all
                       elsif current_api_v1_user.client?
                         Booking.where(brand_id: current_api_v1_user.brand_id).where.not(zone_id: Location.test_zones.pluck(:id))
                       elsif current_api_v1_user.finance_controller?
                         Booking.completed.where(type: ["Survey", "Installation"])
                       else
                         Booking.where(
                           "zone_id IN (?) OR assigned_city_manager_id = ? OR assigned_partner_user_id = ? OR assigned_field_agent1_id = ? OR assigned_field_agent2_id = ?",
                           current_api_v1_user.zonal_manager? ? current_api_v1_user.get_zones_for_zonal_manager.pluck(:id) : [],
                           current_api_v1_user.city_manager? ? current_api_v1_user.id : nil,
                           current_api_v1_user.partner_manager? ? current_api_v1_user.id : nil,
                           current_api_v1_user.field_agent? ? current_api_v1_user.id : nil,
                           current_api_v1_user.field_agent? ? current_api_v1_user.id : nil
                         )
                       end
        @assignments = @assignments.with_brands.with_locations.with_assignments.with_partners.includes(:surveys)
        @assignments = @assignments.where(brand_id: params[:brand_id].split(',')) if params[:brand_id].present? && !current_api_v1_user.client?
        @assignments = @assignments.where(city_id: params[:city_id].split(',')) if params[:city_id].present?
        @assignments = @assignments.where(zone_id: params[:zone_id].split(',')) if params[:zone_id].present?
        @assignments = @assignments.where('customer_name LIKE ?', "%#{params[:customer_name]}%") if params[:customer_name].present?
        if params[:contact_number].present?
          @assignments = @assignments.where(contact_number: params[:contact_number]).or(@assignments.where(alt_contact_number: params[:contact_number]))
        end
        if params[:assigned_city_manager_id].present?
          @assignments = @assignments.where(assigned_city_manager_id: params[:assigned_city_manager_id].split(','))
        end
        if params[:partner_id].present?
          if @type == 'Installation'
            @assignments = @assignments.where(install_partner_id: params[:partner_id].split(','))
          elsif @type == 'Survey'
            @assignments = @assignments.where(survey_partner_id: params[:partner_id].split(','))
          else
            @assignments = @assignments.where(survey_partner_id: params[:partner_id].split(',')).or(@assignments.where(install_partner_id: params[:partner_id].split(',')))
          end
        end
        if params[:agent_id].present?
          @assignments = @assignments.where(assigned_field_agent1_id: params[:agent_id].split(',')).or(@assignments.where(assigned_field_agent2_id: params[:agent_id].split(',')))
        end
        @assignments = @assignments.where(type: params[:type]) if params[:type].present?
        if params[:brand_booking_code].present?
          @assignments = @assignments.where('brand_booking_code LIKE :brand_booking_code', brand_booking_code: "%#{params[:brand_booking_code]}%")
        end
        if params[:ecb_code].present?
          @assignments = @assignments.where('ecb_booking_code LIKE :ecb_code OR ecb_survey_code LIKE :ecb_code OR ecb_install_code LIKE :ecb_code OR ecb_complaint_code LIKE :ecb_code OR ecb_visit_code LIKE :ecb_code', ecb_code: "%#{params[:ecb_code]}%")
        end
        if params[:start_date].present? && params[:end_date].present? && ( @type == 'Booking' || @type == 'Complaint')
          @assignments = @assignments.created_between(params[:start_date], params[:end_date])
        elsif params[:start_date].present? && params[:end_date].present?
          @assignments = @assignments.where('DATE(scheduled_survey_date_time) >= ? AND DATE(scheduled_survey_date_time) <= ?', params[:start_date],
                                            params[:end_date]).or(@assignments.where('DATE(scheduled_install_date_time) >= ? AND DATE(scheduled_install_date_time) <= ?', params[:start_date],
                                            params[:end_date])).or(@assignments.where('DATE(scheduled_visit_date_time) >= ? AND DATE(scheduled_visit_date_time) <= ?', params[:start_date], params[:end_date]))
        end
        if params[:next_follow_up_start].present? && params[:next_follow_up_end].present?
          @assignments = @assignments.where('DATE(next_follow_up_at) >= ? AND DATE(next_follow_up_at) <= ?',
                                            params[:next_follow_up_start],
                                            params[:next_follow_up_end])
        end
        @assignments = @assignments.where(status: params[:status].split(',')) if params[:status].present?
        @assignments = @assignments.where(install_type: params[:install_type].split(',')) if params[:install_type].present?

        return unless params[:timeframe].present?

        case params[:timeframe]
        when 'today'
          today = Time.now.in_time_zone('Kolkata').to_date
          @assignments = @assignments.where('DATE(scheduled_survey_date_time) = :today OR DATE(scheduled_install_date_time) = :today OR DATE(scheduled_visit_date_time) = :today',
                                            today: today)
                                     .or(@assignments.where(status: %i[pending_approval in_progress]))
        when 'upcoming'
          end_of_today = DateTime.now.end_of_day
          @assignments = @assignments.where('scheduled_survey_date_time > :end_of_today OR scheduled_install_date_time > :end_of_today OR scheduled_visit_date_time > :end_of_today',
                                            end_of_today: end_of_today)
        when 'past'
          beginning_of_today = DateTime.now.beginning_of_day
          @assignments = @assignments.where('scheduled_survey_date_time < :beginning_of_today OR scheduled_install_date_time < :beginning_of_today OR scheduled_visit_date_time < :beginning_of_today',
                                            beginning_of_today: beginning_of_today)
        end
      end

      def call_log_params
        params.require(:call_log).permit(:id, :summary, :log_type, :call_connected, :next_follow_up_at)
      end

      def common_params
        %i[remarks assigned_field_agent1_id assigned_field_agent2_id assigned_partner_user_id
           note_for_agent summary_stage1 summary_stage2 payment_remarks sow_status sow_completion_date
           extra_cable_payment_status extra_cable_payment_date survey_readiness_information_date
           installation_readiness_information_date]
      end

      def survey_update_params
        list_params_allowed = %i[scheduled_survey_date_time survey_partner_id customer_survey_confirmed]
        params.require(:assignment).permit(list_params_allowed + common_params)
      end

      def install_update_params
        list_params_allowed = %i[scheduled_install_date_time install_partner_id customer_install_confirmed install_type commission_date]
        params.require(:assignment).permit(list_params_allowed + common_params)
      end

      def customer_update_params
        params.require(:assignment).permit(:customer_sign, :customer_rating, :customer_feedback, :agent_sign, :ecb_sign)
      end

      def complain_close_params
        params.permit(:close_reason, :close_complaint_summary)
      end

      def response_params
        params.require(:response).permit(:attachment_id, :form_section_id, :form_question_id, :response,
                                         images: [],
                                         videos: [])
      end

      def payment_params
        params.require(:payment).permit(:payment_ref_number, :payment_date, :payment_amount, :payment_party_code,
                                        :bank_name, :payment_description, :payment_gateway_id, :customer_vpa,
                                        :customer_bank, :payment_rrn)
      end
    end
  end
end
