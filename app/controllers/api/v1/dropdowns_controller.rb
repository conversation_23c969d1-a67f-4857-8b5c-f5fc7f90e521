module Api
  module V1
    class DropdownsController < ApplicationController
      def index
        options = []

        if dropdowns_params[:type].eql?('booking') && dropdowns_params[:name].eql?('cancel')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'CustomerRefused', label: 'Customer Refused' },
            { value: 'Duplicate', label: 'Duplicate' },
            { value: 'DealerRequest', label: 'Dealer Request' },
            { value: 'NoShow', value: 'No Show' },
            { value: 'IncorrectUpload', label: 'Incorrect Upload' },
            { value: 'OtherReason', label: 'Other Reason' }
          ]
        elsif dropdowns_params[:type].eql?('booking') && dropdowns_params[:name].eql?('log_type')
          options = [
            { value: 'call_me_later', label: 'Call me later' },
            { value: 'cancelled_the_Booking', label: 'Cancelled the booking' },
            { value: 'cee_customer_ready_for_survey', label: 'CEE- Customer ready for survey' },
            { value: "customer_already_has_an_ev_charger_and_does_not_want_another_charger_installation", label: "Customer already has an EV charger and does not want another charger installation" },
            { value: 'customer_did_not_purchase_any_charger_package', label: 'customer_did_not_purchase_any_charger_package' },
            { value: 'customer_refused', label: 'Customer refused' },
            { value: 'customer_scope_of_working_pending', label: 'Customer scope of working pending' },
            { value: 'customer_wants_installation_later', label: 'Customer wants installation later' },
            { value: 'not_picked', label: 'Not picked' },
            { value: 'installation_already_done_by_another_partner', label: 'Installation already done by another partner' },
            { value: 'noc_not_provided_by_society', label: 'NOC not provided by society' },
            { value: 'outstation_approval_awaited', label: 'Outstation approval awaited' },
            { value: "payment_for_extra_cable_is_pending", label: "Payment for extra cable is pending" },
            { value: 'scheduled', label: 'Scheduled' },
            { value: 'site_house_under_construction', label: 'Site / House under construction' },
            { value: 'site_is_good_to_go_for_installation', label: 'Site is good to go for installation' },
            { value: 'survey_installation_done_against_another_survey_booking_no', label: 'Survey installation done against another survey booking no' },
            { value: 'survey_installation_done_against_other_charger_package', label: 'Survey installation done against other charger package' },
            { value: 'survey_installation_done_under_other_dealer', label: 'Survey installation done under other dealer' },
            { value: 'switched_off', label: 'Switched off' },
            { value: 'wants_survey_later', label: 'Wants survey later' },
            { value: 'any_other_reason', label: 'Any other reason' },
          ] 
        elsif dropdowns_params[:type].eql?('survey') && dropdowns_params[:name].eql?('cancel')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'CustomerRefused', label: 'Customer Refused' },
            { value: 'Duplicate', label: 'Duplicate' },
            { value: 'DealerRequest', label: 'Dealer Request' },
            { value: 'NoShow', label: 'No Show' },
            { value: 'IncorrectUpload', label: 'Incorrect Upload' },
            { value: 'OtherReason', label: 'Other Reason' }
          ]
        elsif dropdowns_params[:type].eql?('survey') && dropdowns_params[:name].eql?('log_type')
          options = [
            { value: 'call_me_later', label: 'Call me later' },
            { value: 'cancelled_the_booking', label: 'Cancelled the booking' },
            { value: 'cee_customer_ready_for_survey', label: 'CEE- Customer ready for survey' },
            { value: "customer_already_has_an_ev_charger_and_does_not_want_another_charger_installation", label: "Customer already has an EV charger and does not want another charger installation" },
            { value: 'customer_did_not_purchase_any_charger_package', label: 'Customer did not purchase any charger package' },
            { value: 'customer_refused', label: 'Customer refused' },
            { value: 'customer_scope_of_working_pending', label: 'Customer scope of working pending' },
            { value: 'customer_wants_installation_later', label: 'Customer wants installation later' },
            { value: 'installation_already_done_by_another_partner', label: 'Installation already done by another partner' },
            { value: 'not_picked', label: 'Not picked' },
            { value: 'noc_not_provided_by_society', label: 'NOC not provided by society' },
            { value: 'outstation_approval_awaited', label: 'Outstation approval awaited' },
            { value: "payment_for_extra_cable_is_pending", label: "Payment for extra cable is pending" },
            { value: 'scheduled', label: 'Scheduled' },
            { value: 'site_house_under_construction', label: 'Site / House under construction' },
            { value: 'site_is_good_to_go_for_installation', label: 'Site is good to go for installation' },
            { value: 'survey_installation_done_against_another_survey_booking_no', label: 'Survey installation done against another survey booking no' },
            { value: 'survey_installation_done_against_other_charger_package', label: 'Survey installation done against other charger package' },
            { value: 'survey_installation_done_under_other_dealer', label: 'Survey installation done under other dealer' },
            { value: 'switched_off', label: 'Switched off' },
            { value: 'wants_survey_later', label: 'Wants survey later' },
            { value: 'any_other_reason', label: 'Any other reason' }
          ] 
        elsif dropdowns_params[:type].eql?('installation') && dropdowns_params[:name].eql?('cancel')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'CustomerRefused', label: 'Customer Refused' },
            { value: 'Duplicate', label: 'Duplicate' },
            { value: 'DealerRequest', label: 'Dealer Request' },
            { value: 'NoShow', label: 'NoShow' },
            { value: 'IncorrectUpload', label: 'Incorrect Upload' },
            { value: 'OtherReason', label: 'Other Reason' }
          ]
        elsif dropdowns_params[:type].eql?('installation') && dropdowns_params[:name].eql?('log_type')
          options = [
            { value: 'call_me_later', label: 'Call me later' },
            { value: 'cancelled_the_booking', label: 'Cancelled the booking' },
            { value: "customer_already_has_an_ev_charger_and_does_not_want_another_charger_installation", label: "Customer already has an EV charger and does not want another charger installation" },
            { value: 'customer_did_not_purchase_any_charger_package', label: 'Customer did not purchase any charger package' },
            { value: 'customer_refused', label: 'Customer refused' },
            { value: 'not_picked', label: 'Not picked' },
            { value: 'outstation_approval_awaited', label: 'Outstation approval awaited' },
            { value: 'scheduled', label: 'Scheduled' },
            { value: 'survey_installation_done_against_another_survey_booking_no', label: 'Survey installation done against another survey booking no' },
            { value: 'survey_installation_done_against_other_charger_package', label: 'Survey installation done against other charger package' },
            { value: 'survey_installation_done_under_other_dealer', label: 'Survey installation done under other dealer' },
            { value: 'switched_off', label: 'Switched off' },
            { value: 'wants_survey_later', label: 'Wants survey later' },
            { value: 'any_other_reason', label: 'Any other reason' }
          ] 
        elsif dropdowns_params[:type].eql?('booking') && dropdowns_params[:name].eql?('hold')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'ChargerFaulty', label: 'Charger Faulty' },
            { value: 'ItemMissing', label: 'Item Missing' },
            { value: 'CustomerRequest', label: 'Customer Request' },
            { value: 'DealerRequest',  label: 'Dealer Request' },
            { value: 'OtherReason', label: 'Other Reason' }
          ]
        elsif dropdowns_params[:type].eql?('survey') && dropdowns_params[:name].eql?('hold')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'ChargerFaulty', label: 'Charger Faulty' },
            { value: 'ItemMissing', label: 'Item Missing' },
            { value: 'CustomerRequest', label: 'Customer Request' },
            { value: 'DealerRequest',  label: 'Dealer Request' },
            { value: 'OtherReason', label: 'Other Reason'}
          ]
        elsif dropdowns_params[:type].eql?('survey') && dropdowns_params[:name].eql?('summary_stage1')
          options = [
            { value: 'Apply for Power Load Enhancement', label: 'Apply for Power Load Enhancement' },
            { value: 'Get some civil work done, which is not in SOW for installation package', label: 'Get some civil work done, which is not in SOW for installation package' },
            { value: 'Site is good to go for installation', label: 'Site is good to go for installation' },
            { value: 'Apply for some permissions from local bodies to carry out the installation of chargers', label: 'Apply for some permissions from local bodies to carry out the installation of chargers' },
            { value: 'Other', label: 'Other' }
          ]
        elsif dropdowns_params[:type].eql?('survey') && dropdowns_params[:name].eql?('summary_stage2')
          options = [
            { value: 'Site is good to go for installation', label: 'Site is good to go for installation' },
            { value: 'Commissioning will be done after load enhancement', label: 'Commissioning will be done after load enhancement' }
          ]
        elsif dropdowns_params[:type].eql?('installation') && dropdowns_params[:name].eql?('hold')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'ChargerFaulty', label: 'Charger Faulty' },
            { value: 'ItemMissing', label: 'Item Missing' },
            { value: 'CustomerRequest', label: 'Customer Request' },
            { value: 'DealerRequest',  label: 'Dealer Request' },
            { value: 'OtherReason', label: 'Other Reason'}
          ]
        elsif dropdowns_params[:type].eql?('complaint') && dropdowns_params[:name].eql?('cancel')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'ProblemResolvedOverPhone', label: 'Problem Resolved Over Phone' },
            { value: 'DuplicateComplaintUploaded', label: 'Duplicate Complaint Uploaded' },
            { value: 'IncorrectUpload', label: 'Incorrect Upload' },
            { value: 'OtherReason', label: 'Other Reason'}
          ]
        elsif dropdowns_params[:type].eql?('complaint') && dropdowns_params[:name].eql?('log_type')
          options = [
            { value: 'call_me_later', label: 'Call me later' },
            { value: 'customer_not_available_and_asked_to_call_later', label: 'Customer not available and asked to call later' },
            { value: 'problem_resolved_over_phone', label: 'Problem resolved over phone' },
            { value: 'switched_off', label: 'Switched off' },
            { value: 'vehicle_is_not_available', label: 'Vehicle is not available' },
            { value: 'visit_planned', label: 'Visit planned' },
            { value: 'any_other_reason', label: 'Any other reason' }
         ] 
        elsif dropdowns_params[:type].eql?('visit') && dropdowns_params[:name].eql?('cancel')
          options = [
            { value: 'CustomerUnreachable', label: 'Customer Unreachable' },
            { value: 'ProblemResolvedOverPhone', label: 'Problem Resolved Over Phone' },
            { value: 'DuplicateComplaintUploaded', label: 'Duplicate Complaint Uploaded' },
            { value: 'IncorrectUpload', label: 'Incorrect Upload' },
            { value: 'OtherReason', label: 'Other Reason'}
          ]
        elsif dropdowns_params[:type].eql?('visit') && dropdowns_params[:name].eql?('summary_stage1')
          options = [
            { value: 'Earthing Fault', label: 'Earthing Fault' },
            { value: 'Voltage Parameters are not proper', label: 'Voltage Parameters are not proper' },
            { value: 'Charger has physical damage', label: 'Charger has physical damage' },
            { value: 'MCB box has physical damage', label: 'MCB box has physical damage' },
            { value: 'RCBO box has physical damagee', label: 'RCBO box has physical damage' },
            { value: 'Connection wires are burnt', label: 'Connection wires are burnt' },
            { value: 'Charger internal issue', label: 'Charger internal issue' },
            { value: 'RFID lost', label: 'RFID lost' },
            { value: 'Emergency button is not working/ broken', label: 'Emergency button is not working/ broken' },
            { value: 'RCBO is not working', label: 'RCBO is not working' },
            { value: 'Charger key lost', label: 'Charger key lost' },
            { value: 'Other', label: 'Other' }
          ]
        elsif dropdowns_params[:type].eql?('visit') && dropdowns_params[:name].eql?('log_type')
          options = [
            { value: 'call_me_later', label: 'Call me later' },
            { value: 'customer_not_available_and_asked_to_call_later', label: 'Customer not available and asked to call later' },
            { value: 'problem_resolved_over_phone', label: 'Problem resolved over phone' },
            { value: 'switched_off', label: 'Switched off' },
            { value: 'vehicle_is_not_available', label: 'Vehicle is not available' },
            { value: 'visit_planned', label: 'Visit planned' },
            { value: 'any_other_reason', label: 'Any other reason' }
          ] 
        elsif dropdowns_params[:type].eql?('complaint') && dropdowns_params[:name].eql?('close')
          options = [
            { value: 'BurntWireReplaced', label: 'Burnt wire replaced' },
            { value: 'ChargerToBeReplaced', label: 'Charger to be replaced' },
            { value: 'EVSEOEMNeedsToInspectTheCharger', label: 'EVSE OEM needs to inspect the charger'  },
            { value: 'EmergencyButtonWasPressed,NowRectified', label: 'Emergency button was pressed, now rectified' },
            { value: 'EarthingValueToBeRectifiedByTheCustomer', label: 'Earthing value to be rectified by the customer' },
            { value: 'FaultCodesWereRemovedAndNowOk', label: 'Fault codes were removed and now ok' },
            { value: 'MCBWasTrippedNowOk', label: 'MCB was tripped now ok' },
            { value: 'MCBReplaced', label: 'MCB replaced'  },
            { value: 'NewEarthingToBeDoneByCustomer', label: 'New earthing to be done by customer' },
            { value: 'NewRFIDToBeObtainedByCustomerFromConcernedSupplier', label: 'New RFID to be obtained by the customer from the concerned supplier' },
            { value: 'NewChargerKeyToBeObtainedByCustomerFromConcerned', label: 'New charger key to be obtained by the customer from the concerned'  },
            { value: 'PhaseVoltagesToBeRectifiedByDISCOM', label: 'Phase voltages to be rectified by DISCOM' },
            { value: 'RCBOReplaced', label: 'RCBO replaced' },
            { value: 'RedLightIssueResolved', label: 'Red light issue resolved' },
            { value: 'RCBOWasTrippedNowOk', label: 'RCBO was tripped now ok' },
            { value: 'SoftwareUpdationDoneChargerIsWorkingNow', label: 'Software updation done charger is working now' },
            { value: 'OthersReasons', label: 'Others reasons' }
          ]
        end
        render json: options
      end

      private

      def dropdowns_params
        params.permit(:type, :name)
      end
    end 
  end
end  
