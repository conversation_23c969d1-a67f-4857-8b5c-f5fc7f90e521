module Api
  module V1
    class PartnersController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user, except: %i[index]
      before_action :set_partner, except: %i[index create]
      before_action :set_location, only: %i[remove_location]

      def index
        @partners = if include_all?
                      Partner.includes(:partner_locations).all
                    else
                      Partner.includes(:partner_locations).active
                    end
        @partners = @partners.where('name LIKE ?', "%#{params[:name]}%") if params[:name].present?
        @partners = @partners.where('code LIKE ?', "%#{params[:code]}%") if params[:code].present?
        @partners = @partners.page(params[:page]).per(20) unless params[:all_pages].present?
      end

      def show; end

      def create
        @partner = Partner.new(partner_params)
        if @partner.save
          render :show, status: :created
        else
          render json: @partner.errors, status: :unprocessable_entity
        end
      end

      def update
        if @partner.update(partner_params)
          render :show, status: :ok
        else
          render json: @partner.errors, status: :unprocessable_entity
        end
      end

      def create_location
        @location = PartnerLocation.new(location_params)
        @location.partner_id = @partner.id
        if @location.save
          render :show, status: :created
        else
          render json: @location.errors, status: :unprocessable_entity
        end
      end

      def create_locations
        begin
          partner_location_params[:location_id].each do |location_id|
            @partner.partner_locations.find_or_initialize_by(location_id: location_id).save!
          end
          render :show, status: :created
        rescue => exp
          render json: exp.message, status: :unprocessable_entity
        end
      end

      def remove_location
        if @location.destroy
          render :show, status: :ok
        else
          render json: @location.errors, status: :unprocessable_entity
        end
      end

      private

      def set_partner
        @partner = Partner.find(params[:id])
      end

      def set_location
        @location = @partner.partner_locations.find(params[:location_id])
      end

      def partner_params
        params.require(:partner).permit(:name, :company_name, :code, :contact_person, :contact_number,
                                        :alt_contact_number, :gst, :billing_address, :status)
      end

      def partner_location_params
        params.require(:location).permit(location_id: [])
      end

      def location_params
        params.require(:location).permit(:location_id)
      end
    end
  end
end
