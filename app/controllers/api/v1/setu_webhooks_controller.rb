module Api
  module V1
    class SetuWebhooksController < ApplicationController
      def payment_notification
        # Log the incoming webhook
        Rails.logger.info("Received Setu payment notification: #{params.to_json}")

        # Verify webhook signature if Setu provides one
        unless valid_signature?
          Rails.logger.error('Invalid Setu webhook signature')
          return render json: { error: 'Invalid signature' }, status: :unauthorized
        end

        # Extract payment details
        event_type = params[:eventType]
        status = params[:status] # success
        reference_id = params[:merchantReferenceId] || params[:referenceId]

        # Only process payment.success events
        unless ['payment.success', 'payment.failed'].include?(event_type)
          Rails.logger.info("Ignoring non-payment event: #{event_type}")
          return render json: { status: 'success' }, status: :ok
        end

        # Find the survey based on reference_id
        survey_code = reference_id.insert(1, '-') if reference_id.present?
        survey = Survey.find_by(ecb_survey_code: survey_code)

        if survey.nil?
          Rails.logger.error("Survey not found for reference_id: #{reference_id}")
          return render json: { status: 'success' }, status: :ok
        end

        amount = params[:amount] / 100.to_f
        transaction_id = params[:txnId]
        payment_party_code = survey.code
        payment_gateway_id = params[:id]
        bank_name = 'Kotak Mahindra Bank'
        customer_vpa = params[:customerVpa]
        payment_rrn = params[:rrn]

        # Process the payment based on status
        if status == 'success'
          # Create a payment record
          survey.payments.create!(
            payment_amount: amount,
            payment_ref_number: transaction_id,
            payment_date: DateTime.now,
            payment_party_code:,
            payment_gateway_id:,
            customer_vpa:,
            payment_rrn:,
            bank_name:,
            payment_description: 'SETU UPI Payment'
          )

          # if survey.pending_amount <= amount
          #   survey.update_column(:payment_received, true)
          #   Rails.logger.info("payment_received marked true for survey: #{survey_code}")
          # end

          Rails.logger.info("Payment recorded successfully for survey: #{survey_code}, amount: #{amount}")

          # Send notification to relevant users
          survey.send_payment_email('Successful')
        else
          # Log failed payment
          Rails.logger.error("Payment failed for survey: #{survey_code}, status: #{status}")

          # Send failure notification to relevant users
          survey.send_payment_email('Failed')
        end

        # Acknowledge receipt of webhook
        render json: { status: 'success' }, status: :ok
      end

      private

      def valid_signature?
        # Get the signature from the request headers
        signature = request.headers['X-Setu-Signature']
        Rails.logger.info("Setu signature: #{signature}")

        return false unless signature.present?

        # Get the webhook secret from credentials
        webhook_secret = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :webhook_secret)
        return false unless webhook_secret.present?

        # Get the raw request body
        payload = request.raw_post

        Rails.logger.info("Setu payload: #{payload}")
        # Create the expected signature using HMAC-SHA256
        digest = OpenSSL::HMAC.digest('SHA256', webhook_secret, payload)
        expected_signature = Base64.strict_encode64(digest)
        Rails.logger.info("Expcted signature: #{expected_signature}")

        # Compare the signatures using a secure comparison method to prevent timing attacks
        ActiveSupport::SecurityUtils.secure_compare(signature, expected_signature)
      rescue StandardError => e
        Rails.logger.error("Error validating Setu signature: #{e.message}")
        false
      end
    end
  end
end
