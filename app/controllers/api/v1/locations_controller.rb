module Api
  module V1
    class LocationsController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user, except: %i[index]
      before_action :set_location, only: %i[show update]

      def index
        @locations = if include_all?
                       Location.includes(:parent).all
                     elsif current_api_v1_user.client?
                       Location.includes(:parent).not_test_location
                     else
                       Location.includes(:parent).active
                     end

        @locations = @locations.where(location_type: params[:location_type]) if params[:location_type].present?
        @locations = @locations.where(parent_id: params[:parent_id]) if params[:parent_id].present?
        @locations = @locations.where('name LIKE ?', "%#{params[:name]}%") if params[:name].present?
        @locations = @locations.where('code LIKE ?', "%#{params[:code]}%") if params[:code].present?

        @locations = @locations.page(params[:page]).per(20) unless params[:all_pages].present?
      end

      def show; end

      def create
        @location = Location.new(location_params)
        if @location.save
          render :show, status: :created
        else
          render json: @location.errors, status: :unprocessable_entity
        end
      end

      def update
        if @location.update(location_params)
          render :show, status: :ok
        else
          render json: @location.errors, status: :unprocessable_entity
        end
      end

      private

      def set_location
        @location = Location.find(params[:id])
      end

      def location_params
        params.require(:location).permit(:name, :code, :location_type, :parent_id, :status)
      end
    end
  end
end
