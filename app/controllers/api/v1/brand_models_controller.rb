module Api
  module V1
    class BrandModelsController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user
      before_action :set_brand_model
      before_action :set_pricing, only: %i[update_pricing delete_pricing]
      def show; end

      def create_pricing
        @pricing = BrandModelPricing.new(pricing_params)
        @pricing.brand_model_id = @brand_model.id
        if @pricing.save
          render :show, status: :created
        else
          render json: @pricing.errors, status: :unprocessable_entity
        end
      end

      def update_pricing
        if @pricing.update(pricing_params)
          render :show, status: :ok
        else
          render json: @pricing.errors, status: :unprocessable_entity
        end
      end

      def delete_pricing
        if @pricing.destroy
          render :show, status: :ok
        else
          render json: @pricing.errors, status: :unprocessable_entity
        end
      end

      private

      def set_brand_model
        @brand_model = BrandModel.find(params[:id])
      end

      def set_pricing
        @pricing = BrandModelPricing.find(params[:pricing_id])
      end

      def pricing_params
        params.require(:pricing).permit(:pricing_per_meter, :complementary_cable_length, :start_date, :end_date, :charger_size)
      end
    end
  end
end
