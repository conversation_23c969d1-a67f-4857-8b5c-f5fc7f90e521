module Api
  module V1
    class SurveysController < AssignmentsController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user
      before_action :set_survey, only: %i[update trigger_email generate_payment_link]
      before_action :set_type, only: %i[index]

      def create
        @assignment = Survey.new(survey_params.merge(status: :scheduled))
        @assignment.assigned_city_manager = current_api_v1_user if current_api_v1_user.city_manager?
        if @assignment.save
          render :show, status: :created
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def update
        if @survey.update(update_params)
          render :show, status: :ok
        else
          render json: @survey.errors, status: :unprocessable_entity
        end
      end

      def index
        set_assignments
        @surveys = @assignments.where(type: @type).includes(:booking, :approved_by)
        # paid surveys
        if params[:payment_received].present?
          @surveys = params[:payment_received].eql?('true') ? @surveys.paid : @surveys.unpaid
          @surveys = @surveys.where(brand_model_id: BrandModel.payment_trackerable.ids)
        end
        @surveys = @surveys.page(params[:page]).per(params[:page_size] || 20)
      end

      def trigger_email
        begin
          if email_params['seek_payment_email'].present?
            SurveyMailer.seek_payment_email(@survey, current_api_v1_user.email, extract_emails(email_params['recipient_emails']), email_params['subject'], email_params['seek_payment_email'], extract_emails(email_params['cc_emails']), extract_emails(email_params['bcc_emails']), email_params['attachments']).deliver_now
            @survey.update_columns(seek_payment_email_sent: true)
          elsif email_params['refund_email'].present?
            SurveyMailer.refund_email(@survey, current_api_v1_user.email, extract_emails(email_params['recipient_emails']), email_params['subject'], email_params['refund_email'], extract_emails(email_params['cc_emails']), extract_emails(email_params['bcc_emails']), email_params['attachments']).deliver_now
            @survey.update_columns(refund_email_sent: true)
          elsif email_params['refund_processed_email'].present?
            SurveyMailer.refund_processed_email(@survey, current_api_v1_user.email, extract_emails(email_params['recipient_emails']), email_params['subject'], email_params['refund_processed_email'], extract_emails(email_params['cc_emails']), extract_emails(email_params['bcc_emails']), email_params['attachments']).deliver_now
            @survey.update_columns(refund_processed_email_sent: true)
          end
          render :show, status: :ok
        rescue => exp
          render json: exp.message, status: :unprocessable_entity
        end
      end

      def generate_payment_link
        begin
          payment_link = @survey.payment_link
          render json: { payment_link: }, status: :ok
        rescue => exp
          render json: exp.message, status: :unprocessable_entity
        end
      end

      private

      def set_survey
        @survey = @assignment if @assignment.is_a?(Survey)
      end

      def common_params
        list_params_allowed = %i[customer_name contact_number booking_address city_id pincode scheduled_survey_date_time
                                 brand_model_id model_variant model_colour model_description
                                 model_int_color model_ext_color
                                 alt_contact_number email address_line1 address_line2 locality zone_id state_id
                                 latitude longitude is_outstation is_outstation_for_client company_name gst dealer_gst site_poc_name site_poc_contact
                                 customer_ok_with_recording remarks first_contact_at next_follow_up_at
                                 note_for_agent extra_cable_customer_pay customer_billing is_no_show_during_survey
                                 customer_survey_confirmed summary_stage1 summary_stage2
                                 obc charger_capacity charger_make_type cable_length_new sanctioned_load vdc flag mdi cable_gauge
                                 assigned_field_agent1_id assigned_field_agent2_id
                                 assigned_partner_user_id survey_partner_id customer_city avg_load_utilized
                                 payment_received bhe bhe_date complementary_cable_length pricing_per_meter payment_remarks
                                 is_sow_completed commission_date
                                 sow_status sow_completion_date extra_cable_payment_status extra_cable_payment_date
                                 survey_readiness_information_date installation_readiness_information_date
                                ]
        admin_params = %i[assigned_city_manager_id]
        list_params_allowed += admin_params if current_api_v1_user.mis_user?
        # Add the params only for IMPORT users
        assignment_params = %i[assigned_city_manager_id]
        list_params_allowed += assignment_params if current_api_v1_user.import_user?
        list_params_allowed
      end

      def set_type
        @type = 'Survey'
      end

      def update_params
        params.require(:survey).permit(common_params)
      end

      def email_params
        params.require(:survey).permit(:recipient_emails, :subject, :seek_payment_email, :refund_email, :refund_processed_email, :cc_emails, :bcc_emails, attachments: [])
      end

      def survey_params
        create_params = %i[booking_id]
        params.require(:survey).permit(common_params + create_params)
      end
    end
  end
end
