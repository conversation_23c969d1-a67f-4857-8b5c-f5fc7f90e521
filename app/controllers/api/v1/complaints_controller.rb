module Api
  module V1
    class ComplaintsController < AssignmentsController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user
      before_action :require_import_user, only: %i[import]
      before_action :require_mis_user, only: %i[create]
      before_action :set_complaint, only: %i[update]
      before_action :set_type, only: %i[index]

      # require_mis_user - only allow for MIS and up roles
      def import
        @complaints_import = ComplaintsImport.new(params[:complaints_file], current_api_v1_user)
        @complaints_import.save
        if @complaints_import.errors.any?
          render :import, status: :unprocessable_entity
        else
          render :import, status: :created
        end
      end

      def create
        @assignment = Complaint.new(complaint_params)
        if @assignment.save
          render :show, status: :created
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def index
        set_assignments
        @complaints = @assignments.where(type: @type).includes(:approved_by)
        # unassigned complaints
        @complaints = @complaints.where(status: %i[draft waiting_schedule]) if params[:unassigned].present?
        @complaints = @complaints.page(params[:page]).per(params[:page_size] || 20)
      end

      def update
        if @complaint.update(complaint_params)
          render :show, status: :ok
        else
          render json: @complaint.errors, status: :unprocessable_entity
        end
      end

      private

      def set_complaint
        @complaint = @assignment if @assignment.is_a?(Complaint)
      end

      def complaint_params
        list_params_allowed = %i[brand_model_id install_completed_at brand_complaint_number complaint_receive_date
                                 dealer_name ecb_survey_code ecb_install_code install_partner vin
                                 dealer_location customer_name contact_number alt_contact_number email booking_address
                                 address_line1 address_line2 locality city_id zone_id state_id pincode
                                 latitude longitude is_outstation site_poc_name site_poc_contact complaint_brand_model_name
                                 customer_ok_with_recording remarks company_name gst dealer_gst first_contact_at
                                 next_follow_up_at obc customer_city avg_load_utilized ecb_complaint_code payment_remarks]
        # Add the params only for MIS users
        admin_params = %i[brand_id complaint_receive_date]
        list_params_allowed += admin_params if current_api_v1_user.mis_user?
        # Add the params only for IMPORT users
        assignment_params = %i[assigned_city_manager_id]
        list_params_allowed += assignment_params if current_api_v1_user.import_user?
        params.require(:complaint).permit(list_params_allowed)
      end

      def set_type
        @type = 'Complaint'
      end
    end
  end
end
