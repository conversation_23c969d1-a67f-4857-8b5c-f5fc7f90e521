module Api
  module V1
    class DealersController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user, except: %i[index]
      before_action :set_dealer, only: %i[show update]

      def index
        @dealers = if include_all?
                     BrandDealer.all
                   else
                     BrandDealer.active
                   end

        @dealers = @dealers.where(location_id: params[:location_id]) if params[:location_id].present?
        @dealers = @dealers.where(brand_id: params[:brand_id]) if params[:brand_id].present?

        @dealers = @dealers.page(params[:page]).per(20) unless params[:all_pages].present?
      end

      def show; end

      def create
        @dealer = BrandDealer.new(dealer_params)
        if @dealer.save
          render :show, status: :created
        else
          render json: @dealer.errors, status: :unprocessable_entity
        end
      end

      def update
        if @dealer.update(dealer_params)
          render :show, status: :ok
        else
          render json: @dealer.errors, status: :unprocessable_entity
        end
      end

      private

      def set_dealer
        @dealer = BrandDealer.find(params[:id])
      end

      def dealer_params
        params.require(:dealer).permit(:brand_id, :location_id, :name, :company_name, :code, :branch_code,
                                       :contact_person, :contact_number,
                                       :alt_contact_number, :gst, :address, :billing_address, :status)
      end
    end
  end
end
