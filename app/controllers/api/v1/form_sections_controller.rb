module Api
  module V1
    class FormSectionsController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user, except: %i[index]
      before_action :set_section, only: %i[show update]

      def index
        @sections = if include_all?
                      FormSection.includes(form_section_brands: [:brand, :brand_model]).all
                    else
                      FormSection.includes(form_section_brands: [:brand, :brand_model]).active
                    end

        @sections = @sections.where(section_type: params[:section_type]) if params[:section_type].present?

        @sections = @sections.page(params[:page]).per(20)
      end

      def show; end

      def create
        @section = FormSection.new(section_params)
        if @section.save
          render :show, status: :created
        else
          render json: @section.errors, status: :unprocessable_entity
        end
      end

      def update
        if @section.update(section_params)
          render :show, status: :ok
        else
          render json: @section.errors, status: :unprocessable_entity
        end
      end

      private

      def set_section
        @section = FormSection.find(params[:id])
      end

      def section_params
        params.require(:section).permit(:name, :section_type, :position, :add_to_report, :status, :use_in_installation, :use_in_visit,
                                        :use_in_survey,
                                        form_section_brands_attributes: %i[id brand_id brand_model_id _destroy])
      end
    end
  end
end
