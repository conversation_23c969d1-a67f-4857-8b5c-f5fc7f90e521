module Api
  module V1
    class ReportsController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user
      before_action :parse_params, except: %i[full_report refresh_report]

      def full_report
        filename = "#{params[:report_type]}.xlsx"
        file_key = "reports/#{filename}"
        render json: { download_link: AWS_S3_BUCKET.object(file_key).presigned_url(:get, expires_in: 30) }
      end

      def email_report_request
        report_types = %w[database database_two database_three database_recent_new complaints_database installations invoicing_installation invoicing_survey payment_tracker client_database]
        if report_types.include?(params[:report_type]) && params[:start_date].present? && params[:end_date].present?
          EmailReportRequestJob.perform_later(current_api_v1_user, params[:report_type], filters)
          render json: { success: true  }, status: :ok
        else
          render json: { errors: 'Invalid report type' }, status: :unprocessable_entity
        end
      end

      def refresh_report
        report_types = %w[database complaint_database installations invoicing_installation invoicing_survey payment_tracker]
        if report_types.include?(params[:report_type])
          case params[:report_type]
          when "database"
            DatabaseReportGenerateJob.perform_later
          when "complaint_database"
            ComplaintDatabaseReportGenerateJob.perform_later
          when "installations"
            InstallationsReportGenerateJob.perform_later
          when "invoicing_installation"
            InvoicingInstallationReportGenerateJob.perform_later
          when "invoicing_survey"
            InvoicingSurveyReportGenerateJob.perform_later
          when "payment_tracker"
            PaymentTrackerReportGenerateJob.perform_later
          end
          render json: { success: true  }, status: :ok
        else
          render json: { errors: 'Invalid report type' }, status: :unprocessable_entity
        end
      end

      def database_recent
        @bookings, @surveys, @installations = Report.new(current_api_v1_user, filters, params[:page_number]).database_recent_assignments
      end

      def installations
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).installations_assignments
        send_response('IR_master')
      end

      def database
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).database_assignments.with_database_report_association
      end

      def database_two
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).database_two_assignments.with_database_report_association
      end

      def database_three
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).database_three_assignments.with_database_report_association
      end

      def database_recent_new
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).database_recent_new_assignments.with_database_report_association
      end

      def client_database
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).client_database_assignments.with_database_report_association
      end

      def invoicing_installation
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).invoicing_installation_assignments.with_invoicing_installation_report_association
        send_response('Invoicing_Reports_Installations')
      end

      def invoicing_survey
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).invoicing_survey_assignments.with_invoicing_survey_report_association
        send_response('Invoicing_Reports_Surveys')
      end

      def new_payment_tracker
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).new_payment_tracker_assignments.with_payment_tracker_report_association
        send_response('New_Payment_Tracker')
      end

      def payment_tracker_recent
        @assignments = Report.new(current_api_v1_user, filters, params[:page_number]).payment_tracker_recent_assignments
        send_response('Payment_Tracker_Recent')
      end

      def complaints_database
        @complaints = Report.new(current_api_v1_user, filters, params[:page_number]).complaints_database_assignments
      end

      def client_complaints_database
        @complaints = Complaint.where(created_at: date_range)
        @complaints = @complaints.offset((params[:page_number].to_i - 1)*PAGE_LIMIT).limit(PAGE_LIMIT) if params[:page_number].present?
        @complaints = @complaints
      end

      private

      def send_response(filename)
        respond_to do |format|
          format.xlsx do
            response.headers[
              'Content-Disposition'
            ] = "attachment; filename=#{filename}.xlsx"
          end
          format.html { render json: { error: 'Invalid request' }, status: :unprocessable_entity }
        end
      end

      def parse_params
        if params[:start_date].present? && params[:end_date].present?
          @start_date = params[:start_date]
          @end_date = params[:end_date]
        else
          render json: { error: 'Invalid request' }, status: :unprocessable_entity
        end
      end

      def filters
        {
          start_date: params[:start_date],
          end_date: params[:end_date],
          brand_id: params[:brand_id],
          zone_id: params[:zone_id],
          brand_model_id: params[:brand_model_id]
        }
      end
    end
  end
end
