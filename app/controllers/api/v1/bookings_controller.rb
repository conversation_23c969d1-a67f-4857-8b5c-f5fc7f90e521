module Api
  module V1
    class BookingsController < AssignmentsController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user
      before_action :require_import_user, only: %i[import import_booking_status]
      before_action :require_finance_user, only: %i[import_invoices]
      before_action :require_mis_user, only: %i[create]
      before_action :set_booking, only: %i[update]
      before_action :set_type, only: %i[index]

      # require_mis_user - only allow for MIS and up roles
      def import
        @bookings_import = BookingsImport.new(params[:bookings_file], current_api_v1_user)
        @bookings_import.save
        if @bookings_import.errors.any?
          render :import, status: :unprocessable_entity
        else
          render :import, status: :created
        end
      end

      def import_booking_status
        @bookings_status_import = BookingsStatusImport.new(params[:bookings_file], current_api_v1_user)
        @bookings_status_import.save
        if @bookings_status_import.errors.any?
          render :import_status, status: :unprocessable_entity
        else
          render :import_status, status: :created
        end
      end

      def import_invoices
        @invoice_import = SurveyInvoiceImport.new(params[:bookings_file], current_api_v1_user)
        @invoice_import.save
        if @invoice_import.errors.any?
          render :import_invoices, status: :unprocessable_entity
        else
          render :import_invoices, status: :created
        end
      end

      def import_payment_details
        @import_payment_details = PaymentDetailsImport.new(params[:bookings_file], current_api_v1_user)
        @import_payment_details.save
        if @import_payment_details.errors.any?
          render :import_payment_details, status: :unprocessable_entity
        else
          render :import_payment_details, status: :created
        end
      end

      def import_call_logs
        @call_logs_import = CallLogsImport.new(params[:bookings_file], current_api_v1_user)
        @call_logs_import.save
        if @call_logs_import.errors.any?
          render :import_call_logs, status: :unprocessable_entity
        else
          render :import_call_logs, status: :created
        end
      end

      def transfer_bookings
        @transfer_bookings_import = TransferBookingsImport.new(params[:bookings_file], current_api_v1_user)
        @transfer_bookings_import.save
        if @transfer_bookings_import.errors.any?
          render :transfer_bookings, status: :unprocessable_entity
        else
          render :transfer_bookings, status: :created
        end
      end

      def create
        @assignment = Booking.new(booking_params)
        if @assignment.save
          render :show, status: :created
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def index
        set_assignments
        @bookings = @assignments.where(type: @type).includes(:approved_by)
        # unassigned bookings
        @bookings = @bookings.where(status: %i[draft waiting_schedule]) if params[:unassigned].present?
        @bookings = @bookings.page(params[:page]).per(params[:page_size] || 20)
      end

      def update
        if @booking.update(booking_params)
          render :show, status: :ok
        else
          render json: @booking.errors, status: :unprocessable_entity
        end
      end

      private

      def set_booking
        @booking = @assignment if @assignment.is_a?(Booking)
      end

      def booking_params
        list_params_allowed = %i[brand_model_id model_variant model_colour model_description
                                 model_int_color model_ext_color dealer_name
                                 brand_booking_status dealer_location customer_name
                                 contact_number alt_contact_number email booking_address address_line1
                                 address_line2 locality city_id zone_id state_id pincode
                                 latitude longitude is_outstation is_outstation_for_client site_poc_name site_poc_contact
                                 customer_ok_with_recording remarks company_name gst dealer_gst first_contact_at
                                 next_follow_up_at obc customer_city bhe bhe_date payment_remarks dealer_email
                                 cee_name cee_contact_date city_manager_email zonal_manager_email expected_delivery_date
                                 sow_status sow_completion_date extra_cable_payment_status extra_cable_payment_date
                                 survey_readiness_information_date installation_readiness_information_date charger_issued_date 
                                 customer_proposed_survey_date customer_proposed_survey_date_updated_at customer_proposed_install_date
                                 customer_proposed_install_date_updated_at vin
                                ]
        # Add the params only for MIS users
        admin_params = %i[brand_id ecb_receive_date brand_booking_code brand_booking_date]
        list_params_allowed += admin_params if current_api_v1_user.mis_user?
        # Add the params only for IMPORT users
        assignment_params = %i[assigned_city_manager_id]
        list_params_allowed += assignment_params if current_api_v1_user.import_user?
        params.require(:booking).permit(list_params_allowed)
      end

      def set_type
        @type = 'Booking'
      end
    end
  end
end
