module Api
  module V1
    class VisitsController < AssignmentsController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user
      before_action :set_visit, only: %i[update]

      def create
        @assignment = Visit.new(visit_params.merge(status: :scheduled))
        @assignment.assigned_city_manager = current_api_v1_user if current_api_v1_user.city_manager?
        if @assignment.save
          render :show, status: :created
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def update
        if @visit.update(update_params)
          render :show, status: :ok
        else
          render json: @visit.errors, status: :unprocessable_entity
        end
      end

      def index
        set_assignments
        @visits = @assignments.where(type: 'Visit').includes(:complaint, :approved_by)
        # paid visits
        @visits = @visits.paid if params[:paid]&.eql?('true')
        @visits = @visits.page(params[:page]).per(params[:page_size] || 20)
      end

      private

      def set_visit
        @visit = @assignment if @assignment.is_a?(Visit)
      end

      def common_params
        list_params_allowed = %i[customer_name contact_number booking_address city_id pincode scheduled_visit_date_time
                                 brand_model_id install_completed_at brand_complaint_number complaint_receive_date
                                 dealer_name ecb_survey_code ecb_install_code install_partner vin dealer_location
                                 alt_contact_number email address_line1 address_line2 locality zone_id state_id
                                 latitude longitude is_outstation company_name gst dealer_gst site_poc_name site_poc_contact
                                 customer_ok_with_recording remarks first_contact_at next_follow_up_at
                                 note_for_agent extra_cable_customer_pay customer_billing
                                 customer_visit_confirmed summary_stage1 summary_stage2 complaint_brand_model_name
                                 obc charger_capacity charger_make_type cable_length_new sanctioned_load vdc flag mdi cable_gauge
                                 assigned_field_agent1_id assigned_field_agent2_id
                                 assigned_partner_user_id visit_partner_id customer_city avg_load_utilized payment_remarks]
        admin_params = %i[assigned_city_manager_id]
        list_params_allowed += admin_params if current_api_v1_user.mis_user?
        # Add the params only for IMPORT users
        assignment_params = %i[assigned_city_manager_id]
        list_params_allowed += assignment_params if current_api_v1_user.import_user?
        list_params_allowed
      end

      def update_params
        params.require(:visit).permit(common_params)
      end

      def visit_params
        create_params = %i[complaint_id]
        params.require(:visit).permit(common_params + create_params)
      end
    end
  end
end
