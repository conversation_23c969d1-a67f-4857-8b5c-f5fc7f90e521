module Api
  module V1
    class BrandsController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user, except: %i[index]
      before_action :set_brand, except: %i[index create]
      before_action :set_model, only: %i[update_model]
      before_action :set_charger_type, only: %i[update_charger_type]

      def index
        @brands = if include_all?
                    Brand.includes(:brand_models).all
                  else
                    Brand.includes(:brand_models).active
                  end
        @brands = @brands.where('name LIKE ?', "%#{params[:name]}%") if params[:name].present?
        @brands = @brands.where('code LIKE ?', "%#{params[:code]}%") if params[:code].present?
        @brands = @brands.page(params[:page]).per(20) unless params[:all_pages].present?
      end

      def show; end

      def create
        @brand = Brand.new(brand_params)
        if @brand.save
          render :show, status: :created
        else
          render json: @brand.errors, status: :unprocessable_entity
        end
      end

      def update
        if @brand.update(brand_params)
          render :show, status: :ok
        else
          render json: @brand.errors, status: :unprocessable_entity
        end
      end

      def create_model
        @model = BrandModel.new(model_params)
        @model.brand_id = @brand.id
        if @model.save
          render :show, status: :created
        else
          render json: @model.errors, status: :unprocessable_entity
        end
      end

      def update_model
        if @model.update(model_params)
          render :show, status: :ok
        else
          render json: @model.errors, status: :unprocessable_entity
        end
      end

      def create_charger_type
        @charger_type = ChargerType.new(charger_type_params)
        @charger_type.brand_id = @brand.id
        if @charger_type.save
          render :show, status: :created
        else
          render json: @charger_type.errors, status: :unprocessable_entity
        end
      end

      def update_charger_type
        if @charger_type.update(charger_type_params)
          render :show, status: :ok
        else
          render json: @charger_type.errors, status: :unprocessable_entity
        end
      end

      private

      def set_brand
        @brand = Brand.find(params[:id])
      end

      def set_model
        @model = @brand.brand_models.find(params[:model_id])
      end

      def set_charger_type
        @charger_type = @brand.charger_types.find(params[:charger_type_id])
      end

      def brand_params
        params.require(:brand).permit(:name, :code, :contact_person, :contact_number,
                                      :alt_contact_number, :gst, :billing_address, :status, :brand_logo, :terms_and_conditions, :add_to_forms)
      end

      def model_params
        params.require(:model).permit(:name, :code, :status, :charger_size, :is_required_in_payment_tracker)
      end

      def charger_type_params
        params.require(:charger_type).permit(:charger_size, :seek_payment_email_template, :refund_email_template, :refund_processed_email_template)
      end
    end
  end
end
