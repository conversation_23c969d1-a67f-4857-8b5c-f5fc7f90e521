module Api
  module V1
    class FormQuestionsController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_power_user, except: %i[index]
      before_action :set_question, only: %i[show update]

      def index
        @questions = if include_all?
                       FormQuestion.all
                     else
                       FormQuestion.active
                     end

        @questions = @questions.where(question_type: params[:question_type]) if params[:question_type].present?
        @questions = @questions.where(form_section_id: params[:section_id]) if params[:section_id].present?
        @questions = @questions.page(params[:page]).per(200)
        @questions
      end

      def show; end

      def create
        @question = FormQuestion.new(question_params)
        if @question.save
          render :show, status: :created
        else
          render json: @question.errors, status: :unprocessable_entity
        end
      end

      def update
        if @question.update(question_params)
          render :show, status: :ok
        else
          render json: @question.errors, status: :unprocessable_entity
        end
      end

      private

      def set_question
        @question = FormQuestion.find(params[:id])
      end

      def question_params
        params.require(:question).permit(:title, :form_section_id, :question_type, :image_upload, :video_upload,
                                         :description, :position, :add_to_report, :add_to_customer_report, :mandatory, :status,
                                         form_question_brands_attributes: %i[id brand_id brand_model_id _destroy],
                                         choices: [])
      end
    end
  end
end
