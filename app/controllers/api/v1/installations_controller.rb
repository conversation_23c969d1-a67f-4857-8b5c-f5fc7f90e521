module Api
  module V1
    class InstallationsController < AssignmentsController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user
      before_action :set_installation, only: %i[update]
      before_action :set_type, only: %i[index]

      def create
        @assignment = Installation.new(installation_params.merge(status: :scheduled))
        @assignment.assigned_city_manager = current_api_v1_user if current_api_v1_user.city_manager?
        if @assignment.save
          render :show, status: :created
        else
          render json: @assignment.errors, status: :unprocessable_entity
        end
      end

      def update
        if @installation.update(update_params)
          render :show, status: :ok
        else
          render json: @installation.errors, status: :unprocessable_entity
        end
      end

      def index
        set_assignments
        @installations = @assignments.where(type: @type).includes(:booking, :survey, :approved_by)
        # paid installations
        if params[:payment_received].present?
          @installations = params[:payment_received].eql?('true') ? @installations.paid : @installations.unpaid
          @installations = @installations.where(brand_model_id: BrandModel.payment_trackerable.ids)
        end
        @installations = @installations.page(params[:page]).per(params[:page_size] || 20)
      end

      private

      def set_installation
        @installation = @assignment if @assignment.is_a?(Installation)
      end

      def common_params
        list_params_allowed = %i[customer_name contact_number booking_address city_id pincode scheduled_install_date_time
                                 brand_model_id model_variant model_colour model_description
                                 model_int_color model_ext_color
                                 alt_contact_number email address_line1 address_line2 locality zone_id state_id
                                 latitude longitude is_outstation is_outstation_for_client company_name gst dealer_gst site_poc_name site_poc_contact
                                 customer_ok_with_recording remarks first_contact_at next_follow_up_at
                                 note_for_agent extra_cable_customer_pay customer_billing
                                 customer_install_confirmed summary_stage1 summary_stage2 is_extra_material_used is_no_show_during_installation
                                 obc charger_capacity charger_make_type cable_length_new sanctioned_load vdc flag mdi cable_gauge
                                 charger_issued_date install_type survey_done_same_day vin engine_number commission_number
                                 assigned_field_agent1_id assigned_field_agent2_id nature_of_install_remarks old_cable_used_length new_cable_used_length nature_of_work
                                 number_of_old_mcb_rcbo_used number_of_new_mcb_rcbo_used number_of_old_mcb_rcbo_box_used number_of_new_mcb_rcbo_box_used nature_of_install_remarks
                                 assigned_partner_user_id install_partner_id customer_city avg_load_utilized payment_received bhe bhe_date invoice_amount invoice_date invoice_number
                                 complementary_cable_length pricing_per_meter payment_remarks is_sow_completed commission_date
                                 sow_status sow_completion_date extra_cable_payment_status extra_cable_payment_date
                                 survey_readiness_information_date installation_readiness_information_date]
        admin_params = %i[assigned_city_manager_id]
        list_params_allowed += admin_params if current_api_v1_user.mis_user?
                # Add the params only for IMPORT users
        assignment_params = %i[assigned_city_manager_id]
        list_params_allowed += assignment_params if current_api_v1_user.import_user?
        list_params_allowed
      end

      def update_params
        params.require(:installation).permit(common_params)
      end

      def installation_params
        create_params = %i[booking_id survey_id]
        params.require(:installation).permit(common_params + create_params)
      end

      def set_type
        @type = 'Installation'
      end
    end
  end
end
