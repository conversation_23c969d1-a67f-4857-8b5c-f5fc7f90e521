module Api
  module V1
    class UsersController < ApplicationController
      before_action :authenticate_logged_in_user
      before_action :require_editor_user, except: %i[me save_fcm_data]
      before_action :set_user, except: %i[me index create save_fcm_data]
      before_action :set_role, only: %i[update_role remove_role]
      before_action :authenticate_super_user_role, only: %i[create_role update_role remove_role]

      def me
        @user = current_user
      end

      def save_fcm_data
        @user = current_user
        user_fcm_registration = @user.user_fcm_registrations.find_or_initialize_by(fcm_registration_token: fcm_params[:fcm_registration_token])
        user_fcm_registration.assign_attributes(fcm_params)
        if user_fcm_registration.save
          render :me, status: :ok
        else
          render json: user_fcm_registration.errors, status: :unprocessable_entity
        end
      end

      def index
        @users = if include_all?
                   User.includes(user_roles: [:brand, :partner, location: :parent]).all
                 else
                   User.includes(user_roles: [:brand, :partner, location: :parent]).active
                 end

        @users = @users.where(user_roles: { role_type: params[:role_type] }) if params[:role_type].present?
        @users = @users.where(user_roles: { location_id: params[:location_id] }) if params[:location_id].present?
        @users = @users.where(user_roles: { brand_id: params[:brand_id] }) if params[:brand_id].present?
        @users = @users.where(user_roles: { partner_id: params[:partner_id] }) if params[:partner_id].present?
        @users = @users.where('name LIKE ?', "%#{params[:name]}%") if params[:name].present?
        @users = @users.where('email LIKE ?', "%#{params[:email]}%") if params[:email].present?
        @users = @users.where('mobile LIKE ?', "%#{params[:mobile]}%") if params[:mobile].present?

        @users = @users.page(params[:page]).per(20) unless params[:all_pages].present?
      end

      def show; end

      def create
        @user = User.new(user_params)
        if @user.save
          render :show, status: :created
        else
          render json: @user.errors, status: :unprocessable_entity
        end
      end

      def update
        if @user.update(user_params)
          render :show, status: :ok
        else
          render json: @user.errors, status: :unprocessable_entity
        end
      end

      def create_role
        @role = UserRole.new(role_params)
        @role.user_id = @user.id
        if @role.save
          render :show, status: :created
        else
          render json: @role.errors, status: :unprocessable_entity
        end
      end

      def create_roles
        create_role && return if roles_params[:location_id].nil?
        begin
          roles_params[:location_id].each do |location_id|
            user_role_params = roles_params
            user_role_params[:location_id] = location_id
            @user.user_roles.find_or_initialize_by(user_role_params).save!
          end
          render :show, status: :created
        rescue => exp
          render json: exp.message, status: :unprocessable_entity
        end
      end

      def update_role
        if @role.update(role_params)
          render :show, status: :ok
        else
          render json: @role.errors, status: :unprocessable_entity
        end
      end

      def remove_role
        if @role.destroy
          render :show, status: :ok
        else
          render json: @role.errors, status: :unprocessable_entity
        end
      end

      def transfer_bookings
        new_city_manager = User.find(transfer_params[:city_manager_id])
        render json: { error: 'City manager not found' }, status: :unprocessable_entity unless new_city_manager.present?

        bookings = Booking.where.not(status: :completed, type: "Installation").where(assigned_city_manager_id: @user.id)

        if bookings.update_all(assigned_city_manager_id: new_city_manager.id)
          render :show, status: :ok
        else
          render json: { error: 'Something went wrong' }, status: :unprocessable_entity
        end
      end

      private

      def set_user
        @user = User.find(params[:id])
      end

      def set_role
        @role = @user.user_roles.find(params[:role_id])
      end

      def user_params
        params.require(:user).permit(:name, :email, :password, :password_confirmation,
                                     :mobile, :alternate_mobile, :aadhaar, :employee_id, :status, :user_type, :brand_id)
      end

      def role_params
        params.require(:role).permit(:role_type, :location_id, :brand_id, :partner_id)
      end

      def roles_params
        params.require(:role).permit(:role_type, :brand_id, :partner_id, location_id: [])
      end

      def fcm_params
        params.require(:user).permit(:fcm_registration_token, :device_id, :device_os, :device_os_version, :device_name,
                                     :device_app_version, :device_app_build_number)
      end

      def transfer_params
        params.require(:user).permit(:city_manager_id)
      end

      def authenticate_super_user_role
        if (@role&.user&.super_user? || params[:role_type].eql?("super_user")) && !current_user.super_user?
          render json: { error: 'Unauthorized to assign this role' }, status: :unprocessable_entity
        end
      end
    end
  end
end
