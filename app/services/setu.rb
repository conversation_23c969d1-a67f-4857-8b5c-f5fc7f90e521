module Setu
  class Client
    def initialize
      @client_id = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :client_id)
      @client_secret = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :client_secret)
      @merchant_id = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :merchant_id)
      @merchant_vpa = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :merchant_vpa)
      @login_url = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :login_url)
      @qr_url = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :qr_url)
      @lambda_url = Rails.application.credentials[Rails.env.to_sym].dig(:setu, :lambda_url)
    end

    # Generate QR code using Lambda function
    def generate_qr_via_lambda(amount, survey_code, transaction_note = nil)
      transaction_note ||= "Payment for #{survey_code}"

      Rails.logger.info("Calling Setu Lambda for QR generation: amount=#{amount}, survey_code=#{survey_code}")

      begin
        connection = Faraday.new(url: @lambda_url)

        response = connection.post do |req|
          req.headers['Content-Type'] = 'application/json'
          req.body = {
            amount:,
            surveyCode: survey_code,
            transactionNote: transaction_note
          }.to_json
        end

        if response.success?
          data = JSON.parse(response.body)
          Rails.logger.info("Successfully generated QR code via Lambda for survey: #{survey_code}")
          data
        else
          Rails.logger.error("Failed to generate QR code via Lambda: #{response.status} - #{response.body}")
          { error: "Failed to generate QR code: #{response.status}", details: response.body }
        end
      rescue Faraday::Error => e
        Rails.logger.error("Network error calling Lambda: #{e.message}")
        { error: "Network error: #{e.message}" }
      rescue JSON::ParserError => e
        Rails.logger.error("Invalid JSON response from Lambda: #{e.message}")
        { error: "Invalid response format: #{e.message}" }
      rescue StandardError => e
        Rails.logger.error("Unexpected error calling Lambda: #{e.message}")
        { error: "Unexpected error: #{e.message}" }
      end
    end

    def generate_qr(amount, survey_code, transaction_note)
      # Get access token
      access_token = get_access_token
      return { error: 'Failed to get access token' } unless access_token

      # Generate QR code
      generate_qr_code(access_token, amount, survey_code, transaction_note)
    end

    private

    def get_access_token
      connection = Faraday.new(url: @login_url)

      response = connection.post do |req|
        req.headers['client'] = 'bridge'
        req.headers['Content-Type'] = 'application/json'
        req.body = {
          clientID: @client_id,
          secret: @client_secret,
          grant_type: 'client_credentials'
        }.to_json
      end

      if response.success?
        data = JSON.parse(response.body)
        Rails.logger.info('Setu login successful')
        data['access_token']
      else
        Rails.logger.error("Setu login failed: #{response.body}")
        nil
      end
    end

    def generate_qr_code(access_token, amount, survey_code, transaction_note)
      connection = Faraday.new(url: @qr_url)

      # Set expiry date to 30 day from now
      expiry_date = (Time.now + 30.days).iso8601

      response = connection.post do |req|
        req.headers['Authorization'] = "Bearer #{access_token}"
        req.headers['content-type'] = 'application/json'
        req.headers['merchantId'] = @merchant_id
        req.body = {
          amount:,
          expiryDate: expiry_date,
          merchantVpa: @merchant_vpa,
          metadata: {
            surveyCode: survey_code
          },
          minAmount: amount,
          referenceId: survey_code.tr('-', ''),
          transactionNote: transaction_note
        }.to_json
      end

      if response.success?
        Rails.logger.info("Setu QR code generated successfully for survey: #{survey_code}")
        JSON.parse(response.body)
      else
        Rails.logger.error("Setu QR code generation failed for survey: #{survey_code}, error: #{response.body}")
        { error: 'Failed to generate QR code', details: response.body }
      end
    end
  end
end
