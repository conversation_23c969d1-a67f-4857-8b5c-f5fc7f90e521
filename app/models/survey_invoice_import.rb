class SurveyInvoiceImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :imported_count, :uploaded_by, :update_hash

  def initialize(invoices_file = nil, current_user)
    @file = invoices_file
    @uploaded_by = current_user
    @update_hash = {}
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    when '.xlsx'
      Roo::Spreadsheet.open(file, extension: :xlsx)
    else
      raise "Unknown file type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    headers = spreadsheet.row(1)

    # Validate headers
    required_headers = ['Survey number', 'Invoice number', 'Invoice date', 'Invoice total amount', 'Invoice Remarks']
    missing_headers = required_headers - headers

    if missing_headers.any?
      errors.add :base, "Missing required columns: #{missing_headers.join(', ')}"
      return []
    end

    ecb_survey_codes = []

    spreadsheet.each_with_index do |row, idx|
      next if idx == 0
      ecb_survey_codes[idx] = row[1]
    end

    if ecb_survey_codes.count > 1000
      errors.add :base, "Exceeds limit: Max 1000 records can be updated"
      return
    end

    spreadsheet.each_with_index do |row, idx|
      next if idx == 0

      invoice_details = Hash[[headers, row].transpose]

      if invoice_details['Survey number'].nil?
        errors.add :base, "Row #{idx + 1}, Serial No #{invoice_details['Serial No']}: ECB survey code is blank"
        next
      elsif ecb_survey_codes.compact.tally[invoice_details['Survey number']] > 1
        errors.add :base, "Row #{idx + 1}, Serial No #{invoice_details['Serial No']}: has duplicate ECB survey code"
        next
      end

      invoice_number = invoice_details['Invoice number']

      if invoice_number.blank?
        errors.add :base, "Row #{idx + 1}, Serial No #{invoice_details['Serial No']}: Invoice number is blank"
        next
      end

      invoice_date = Date.strptime(invoice_details['Invoice date'], "%d/%m/%Y") rescue nil

      if invoice_details['Invoice date'].present? && invoice_date.nil?
        errors.add :base, "Row #{idx + 1}, Serial No #{invoice_details['Serial No']}: Invalid Invoice date or format, expected format is dd/mm/yyyy"
        next
      end

      invoice_amount = invoice_details['Invoice total amount']

      if invoice_amount.blank?
        errors.add :base, "Row #{idx + 1}, Serial No #{invoice_details['Serial No']}: Invoice total amount is blank"
        next
      elsif invoice_amount.to_f.zero?
        errors.add :base, "Row #{idx + 1}, Serial No #{invoice_details['Serial No']}: Invoice total amount is zero"
        next
      else
        invoice_amount = invoice_amount.to_f
      end

      update_hash[invoice_details['Survey number']] = {
        invoice_number:,
        invoice_date:,
        invoice_amount:,
        invoice_remarks: invoice_details['Invoice Remarks']
      }
    end

    return if errors.any?

    ecb_survey_codes = update_hash.keys.compact
    surveys = Survey.where(ecb_survey_code: ecb_survey_codes).select(:id, :ecb_survey_code)

    missing_ecb_survey_codes = ecb_survey_codes - surveys.pluck(:ecb_survey_code)

    if missing_ecb_survey_codes.any?
      errors.add :base, "No surveys for ecb survey codes: #{missing_ecb_survey_codes.join(', ')}"
      return
    end

    surveys
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    imported_items
    return if errors.any?

    update_array = []

    imported_items.each do |survey|
      survey_code = survey.ecb_survey_code
      update_array << {
        id: survey.id,
        invoice_number: update_hash[survey_code][:invoice_number],
        invoice_date: update_hash[survey_code][:invoice_date],
        invoice_amount: update_hash[survey_code][:invoice_amount],
        invoice_remarks: update_hash[survey_code][:invoice_remarks]
      }
    end

    Survey.upsert_all(update_array)
    @imported_count = imported_items.size

    true
  end
end
