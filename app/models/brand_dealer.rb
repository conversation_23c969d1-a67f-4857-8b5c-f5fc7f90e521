class BrandDealer < ApplicationRecord
  belongs_to :brand
  belongs_to :location
  validates :name, presence: true, length: { minimum: 3, maximum: 30 }
  validates :code, allow_blank: true, length: { minimum: 2, maximum: 10 }

  validates :name, uniqueness: { case_sensitive: false, scope: %i[brand] }
  validates :code, uniqueness: { case_sensitive: false, scope: %i[brand] }

  validates :branch_code, allow_blank: true, length: { minimum: 2, maximum: 10 }
  validates :company_name, allow_blank: true, length: { minimum: 3, maximum: 50 }

  validates :contact_person, allow_blank: true, length: { minimum: 3, maximum: 30 }
  validates :contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :alt_contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :billing_address, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :address, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :gst, allow_blank: true, length: { minimum: 15, maximum: 15 }

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  scope :active, -> { where(status: :active) }

  default_scope { order(:name) }

  validate :correct_location_type

  def correct_location_type
    return if location.location_type_city?

    errors.add(:location, '- Incorrect location selected for Dealer')
  end

  rails_admin do
    label 'Dealerships'
  end
end
