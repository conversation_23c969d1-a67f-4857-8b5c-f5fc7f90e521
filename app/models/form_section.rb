class FormSection < ApplicationRecord
  validates :name, uniqueness: { case_sensitive: false }, presence: true, length: { minimum: 3, maximum: 30 }
  validates :position, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  has_one_attached :icon
  has_many :form_section_brands, dependent: :destroy
  has_many :form_questions, dependent: :nullify
  has_many :form_submissions, dependent: :nullify
  accepts_nested_attributes_for :form_section_brands, allow_destroy: true

  validate :correct_usage

  # after_create :associate_brands

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  enum section_type: {
    in_survey: 0,
    pre_survey: 1,
    post_survey: 2,
    in_visit: 3,
    pre_visit: 4,
    post_visit: 5
  }, _prefix: true

  scope :active, -> { where(status: :active) }
  scope :installation, -> { where(use_in_installation: true) }
  scope :survey, -> { where(use_in_survey: true) }
  scope :visit, -> { where(use_in_visit: true) }

  default_scope { order(:position) }

  def form_section_brands_attributes=(*args)
    associate_brands if args.blank? || args.empty? || args[0].blank? || args[0].empty?
    super(*args)
  end

  rails_admin do
    exclude_fields :form_section_brands, :form_questions, :form_submissions
  end

  private

  def associate_brands
    return unless form_section_brands.count.zero?

    Brand.active.each do |brand|
      form_section_brands.build brand_id: brand.id
      # joiner = form_section_brands.build brand_id: brand.id
      # joiner.save!
    end
  end

  def correct_usage
    return if use_in_installation? || use_in_survey? || use_in_visit?

    errors.add(:use_in_installation, 'Atleast one of the usage flags should be true')
    errors.add(:use_in_survey, 'Atleast one of the usage flags should be true')
    errors.add(:use_in_visit, 'Atleast one of the usage flags should be true')
  end
end
