class FormSubmission < ApplicationRecord
  belongs_to :formable, polymorphic: true
  belongs_to :form_section
  belongs_to :form_question
  has_many_attached :images
  has_many_attached :videos

  validates :response, allow_blank: true, length: { minimum: 1, maximum: 1024 }

  validates :form_question, uniqueness: { scope: %i[formable] }

  validate :correct_usage
  before_create :set_original_values
  after_save :set_linked_values

  private

  def set_linked_values
    return if form_question.linked_field.blank?

    return unless formable.respond_to? form_question.linked_field

    formable.update_attribute(form_question.linked_field, response)
  end

  def correct_usage
    unless (form_section.use_in_installation? && formable.type == 'Installation') ||
           (form_section.use_in_survey? && formable.type == 'Survey') ||
           (form_section.use_in_visit? && formable.type == 'Visit')
      errors.add(:form_section, 'Invalid usage')
    end
    if form_question.question_type_boolean? && response.present? && !%w[true false].include?(response)
      errors.add(:response, 'Invalid boolean response')
    end
    if form_question.question_type_single_choice? && response.present? && !form_question.choices.include?(response)
      errors.add(:response, 'Invalid single choice response')
    end
    if form_question.question_type_multi_choice? && response.present?
      response.split(',').each do |choice|
        errors.add(:response, 'Invalid multi choice response') unless form_question.choices.include?(choice.to_s)
      end
    end
    errors.add(:images, 'Image upload mandatory') if form_question.image_upload_mandatory? && images.empty?
    errors.add(:videos, 'Video upload mandatory') if form_question.video_upload_mandatory? && videos.empty?
    if form_question.image_upload_optional? && images.attached? && images.size > 5
      errors.add(:images, 'Maximum 5 images allowed')
    end
    if form_question.video_upload_optional? && videos.attached? && videos.size > 5
      errors.add(:videos, 'Maximum 5 videos allowed')
    end
    errors.add(:images, 'Image upload not allowed') if form_question.image_upload_no? && images.attached?
    errors.add(:videos, 'Video upload not allowed') if form_question.video_upload_no? && videos.attached?

    # validate data type and size of images and videos
    images.each do |image|
      errors.add(:images, 'Invalid image type') unless image.content_type.in?(%w[image/jpeg image/png application/pdf])
      errors.add(:images, 'Image size too large') if image.byte_size > 15.megabytes
    end

    videos.each do |video|
      errors.add(:videos, 'Invalid video type') unless video.content_type.starts_with?('video/')
      errors.add(:videos, 'Video size too large') if video.byte_size > 250.megabytes
    end
  end

  def set_original_values
    self.section_name = form_section&.name
    self.question_title = form_question&.title
    self.position = form_question&.position
    self.add_to_report = form_question&.add_to_report
  end
end
