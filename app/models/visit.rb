class Visit < Booking
  belongs_to :survey, class_name: 'Survey', optional: true
  belongs_to :complaint, class_name: '<PERSON><PERSON><PERSON><PERSON>', optional: true

  has_many :customer_connect_logs, as: :loggable, class_name: 'CustomerConnectLog', dependent: :destroy
  has_many :form_submissions, as: :formable, dependent: :destroy

  validates_presence_of :scheduled_visit_date_time, :assigned_city_manager_id, :visit_partner_id
  validates :ecb_visit_code, allow_blank: true, uniqueness: { case_sensitive: false }, if: -> { type == 'Visit' }
  validate :correct_assignments, unless: :skip_validations_callback

  before_validation :copy_parent_data, on: :create
  before_validation :set_ecb_visit_code, on: :create

  before_update :change_status, if: :status_on_hold?
  after_create :set_complaint_as_scheduled
  after_update :set_complaint_as_assigned, if: :status_scheduled?

  def change_status
    return unless scheduled_visit_date_time_changed?

    Rails.logger.info('Setting as scheduled')
    self.status = :scheduled
  end

  # Visit states: scheduled -> in_progress -> on_hold -> pending_approval -> completed, cancelled

  rails_admin do
    exclude_fields :type, :brand_dealer, :brand_dealer_id,
                   :ecb_receive_date, :ecb_complaint_code, :ecb_install_code,
                   :scheduled_install_date_time, :actual_install_date_time, :install_completed_at, :install_partner, :visit,
                   :layout_approved_at, :layout_approved_by, :charger_issued_date, :extra_cable_customer_pay,
                   :vin, :engine_number, :install_type, :visit_id, :customer_install_confirmed, :uploaded_by,
                   :customer_connect_logs, :form_submissions, :installations
  end

  def title
    "#{ecb_visit_code} - #{customer_name} - #{contact_number}"
  end

  private

  def copy_parent_data
    return unless complaint

    self.brand_id = complaint.brand_id
    self.dealer_name = complaint.dealer_name
    self.dealer_location = complaint.dealer_location
    self.ecb_receive_date = complaint.ecb_receive_date
    self.complaint_receive_date = complaint.complaint_receive_date
    self.ecb_complaint_code = complaint.ecb_complaint_code
    self.ecb_survey_code = complaint.ecb_survey_code
    self.ecb_install_code = complaint.ecb_install_code
    self.vin = complaint.vin
    self.brand_complaint_number = complaint.brand_complaint_number
    self.actual_install_date_time = complaint.actual_install_date_time
    self.customer_city = complaint.customer_city
    self.complaint_brand_model_name = complaint.complaint_brand_model_name
  end

  def correct_assignments
    if assigned_city_manager_id && (!assigned_city_manager || !assigned_city_manager.status_active? || !assigned_city_manager.city_manager?)
      errors.add(:assigned_city_manager_id,
                 'is not active')
    end
    if visit_partner_id && (!visit_partner || !visit_partner.status_active?)
      errors.add(:visit_partner_id,
                 'is not active')
    end
    if assigned_field_agent1_id && (!assigned_field_agent1 || !assigned_field_agent1.status_active?)
      errors.add(:assigned_field_agent1_id,
                 'is not active')
    end
    if assigned_field_agent2_id && (!assigned_field_agent2 || !assigned_field_agent2.status_active?)
      errors.add(:assigned_field_agent2_id,
                 'is not active')
    end
    if assigned_partner_user_id && (!assigned_partner_user || !assigned_partner_user.status_active?)
      errors.add(:assigned_partner_user_id,
                 'is not active')
    end
    if assigned_field_agent1
      if visit_partner_id.blank? || assigned_field_agent1.partner_id != visit_partner_id
        errors.add(:assigned_field_agent1_id,
                   'does not belong to visit partner')
      end
      errors.add(:assigned_field_agent1_id, 'is not a field agent') unless assigned_field_agent1.field_agent?
    end
    if assigned_field_agent2
      if visit_partner_id.blank? || assigned_field_agent2.partner_id != visit_partner_id
        errors.add(:assigned_field_agent2_id,
                   'does not belong to visit partner')
      end
      errors.add(:assigned_field_agent2_id, 'is not a field agent') unless assigned_field_agent2.field_agent?
    end
    if assigned_partner_user
      if visit_partner_id.blank? || assigned_partner_user.partner_id != visit_partner_id
        errors.add(:assigned_partner_user_id,
                   'does not belong to visit partner')
      end
      errors.add(:assigned_partner_user_id, 'is not a partner manager') unless assigned_partner_user.partner_manager?
    end
    if assigned_field_agent1_id && assigned_field_agent2_id && assigned_field_agent1_id == assigned_field_agent2_id
      errors.add(:assigned_field_agent2_id, 'is same as Field Agent 1')
    end
  end

  def set_ecb_visit_code
    return unless type == 'Visit'

    model_code = brand_model ? brand_model.code : '00'
    today = Time.now.in_time_zone('Kolkata').to_date

    prefix = "V-#{brand.code}#{model_code}#{today.strftime('%y%b')}#{city.code}".upcase

    running_count = Visit.where('ecb_visit_code LIKE ?',
                                 "#{prefix}%").order(ecb_visit_code: :desc).first.try(:ecb_visit_code)

    counter = running_count ? running_count.gsub(prefix, '').to_i : 0
    counter += 1

    self.ecb_visit_code = "#{prefix}#{counter}"

    loop do
      break unless self.class.exists?(ecb_visit_code:)

      counter += 1
      self.ecb_visit_code = "#{prefix}#{counter}"
    end
  end

  def set_complaint_as_scheduled
    complaint.update_columns(status: :scheduled, customer_visit_confirmed: true) unless complaint.status_scheduled?
  end

  def set_complaint_as_assigned
    complaint.update_column(:status, :assigned) unless complaint.status_assigned?
  end
end
