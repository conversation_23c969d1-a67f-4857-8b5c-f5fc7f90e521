class Installation < Booking
  validates :scheduled_install_date_time, presence: true

  validates :assigned_city_manager_id, presence: true

  # t.datetime :actual_install_date_time - on addition,  validate if date is not in future
  # t.datetime :install_completed_at - on addition,  validate if date is not in future

  belongs_to :booking, class_name: 'Booking'
  belongs_to :survey, class_name: 'Survey'
  has_many :customer_connect_logs, as: :loggable, class_name: 'CustomerConnectLog', dependent: :destroy
  has_many :payments, as: :payable, class_name: 'Payment', dependent: :destroy
  has_many :refunds, as: :payable, class_name: 'Refund', dependent: :destroy

  scope :paid, -> { includes(:survey).where(status: :completed).where("survey.payment_received": true) }
  scope :unpaid, -> { includes(:survey).where(status: :completed).where("survey.payment_received": false) }

  validates :install_partner_id, presence: true
  # validates :assigned_partner_user_id, presence: true

  has_many :form_submissions, as: :formable, dependent: :destroy

  before_validation :copy_parent_data, on: :create

  validate :correct_assignments, unless: :skip_validations_callback

  validates :ecb_install_code, allow_blank: true, uniqueness: { case_sensitive: false }, if: lambda {
                                                                                               type == 'Installation'
                                                                                             }

  before_validation :set_ecb_install_code, on: :create

  before_update :change_status, if: :status_on_hold?
  after_commit :notify_customer, unless: :skip_validations_callback, if:-> { brand&.automated_emails_enabled? }

  def change_status
    return unless scheduled_install_date_time_changed?

    Rails.logger.info('Setting as scheduled')
    self.status = :scheduled
  end

  # Installation states: scheduled -> in_progress -> on_hold -> pending_approval -> completed, cancelled

  after_create :set_booking_as_scheduled

  rails_admin do
    exclude_fields :type, :brand, :brand_dealer, :brand_id, :brand_dealer_id, :brand_booking_date,
                   :ecb_receive_date, :customer_name, :contact_number, :email, :booking_address,
                   :address_line1, :address_line2, :city, :zone, :state, :pincode, :company_name, :gst,
                   :ecb_booking_code, :ecb_survey_code,
                   :scheduled_survey_date_time, :actual_survey_date_time, :survey_completed_at, :survey_partner,
                   :customer_survey_confirmed, :uploaded_by, :customer_connect_logs, :form_submissions
  end

  def title
    "#{ecb_install_code} - #{customer_name} - #{contact_number}"
  end

  def invoice_total
    @invoice_total ||= base_amount + gst_amount
  end

  def base_amount
    @base_amount ||= extra_cable_length * pricing_per_meter.to_f
  end

  def gst_amount
    @gst_amount ||= base_amount * (GST_PERCENTAGE.to_f/100)
  end

  def extra_cable_length
    if cable_length_new.to_f > complementary_cable_length.to_f
      cable_length_new.to_f - [survey.cable_length_new.to_f, complementary_cable_length.to_f].max
    elsif survey.cable_length_new.to_f > complementary_cable_length.to_f
      complementary_cable_length.to_f - survey.cable_length_new.to_f
    else
      0
    end
  end

  def cable_amount
    if cable_length_new.to_f > complementary_cable_length.to_f
      (cable_length_new.to_f - complementary_cable_length.to_f) * pricing_per_meter.to_f
    else
      0
    end
  end

  def cable_amount_with_gst
    @cable_amount_with_gst ||= cable_amount * ((100 + GST_PERCENTAGE.to_f)/100)
  end

  def amount_to_receive
    @amount_to_receive ||= [cable_amount_with_gst - amount_received - survey.amount_received, 0].max
  end

  def amount_to_refund
    @amount_to_refund ||= survey.amount_received + amount_received - cable_amount_with_gst
  end

  # def latest_comment
  #   CustomerConnectLog.where(loggable_id: [booking_id, survey_id, id]).order(created_at: :desc).first&.log_type&.capitalize&.gsub('_', ' ')
  # end

  private

  def copy_parent_data
    return unless booking

    self.brand_id = booking.brand_id
    self.dealer_name = booking.dealer_name
    self.dealer_location = booking.dealer_location
    self.ecb_receive_date = booking.ecb_receive_date
    self.brand_booking_code = booking.brand_booking_code
    self.brand_booking_date = booking.brand_booking_date
    self.ecb_booking_code = booking.ecb_booking_code

    return unless survey

    self.scheduled_survey_date_time = survey.scheduled_survey_date_time
    self.actual_survey_date_time = survey.actual_survey_date_time
    self.survey_completed_at = survey.survey_completed_at
    self.survey_partner_id = survey.survey_partner_id
    self.pricing_per_meter = survey.pricing_per_meter
    self.complementary_cable_length = survey.complementary_cable_length
    self.customer_survey_confirmed = survey.customer_survey_confirmed
    self.cee_name = survey.cee_name
    self.cee_contact_date = survey.cee_contact_date
    self.charger_issued_date = survey.charger_issued_date
  end

  def correct_assignments
    if assigned_city_manager_id && (!assigned_city_manager || !assigned_city_manager.status_active? || !assigned_city_manager.city_manager?)
      errors.add(:assigned_city_manager_id,
                 'is not active')
    end
    if install_partner_id && (!install_partner || !install_partner.status_active?)
      errors.add(:install_partner_id,
                 'is not active')
    end
    if assigned_field_agent1_id && (!assigned_field_agent1 || !assigned_field_agent1.status_active?)
      errors.add(:assigned_field_agent1_id,
                 'is not active')
    end
    if assigned_field_agent2_id && (!assigned_field_agent2 || !assigned_field_agent2.status_active?)
      errors.add(:assigned_field_agent2_id,
                 'is not active')
    end
    if assigned_partner_user_id && (!assigned_partner_user || !assigned_partner_user.status_active?)
      errors.add(:assigned_partner_user_id,
                 'is not active')
    end
    if assigned_field_agent1
      if install_partner_id.blank? || assigned_field_agent1.partner_id != install_partner_id
        errors.add(:assigned_field_agent1_id,
                   'does not belong to installation partner')
      end
      errors.add(:assigned_field_agent1_id, 'is not a field agent') unless assigned_field_agent1.field_agent?
    end
    if assigned_field_agent2
      if install_partner_id.blank? || assigned_field_agent2.partner_id != install_partner_id
        errors.add(:assigned_field_agent2_id,
                   'does not belong to installation partner')
      end
      errors.add(:assigned_field_agent2_id, 'is not a field agent') unless assigned_field_agent2.field_agent?
    end
    if assigned_partner_user
      if install_partner_id.blank? || assigned_partner_user.partner_id != install_partner_id
        errors.add(:assigned_partner_user_id,
                   'does not belong to installation partner')
      end
      errors.add(:assigned_partner_user_id, 'is not a partner manager') unless assigned_partner_user.partner_manager?
    end
    if assigned_field_agent1_id && assigned_field_agent2_id && assigned_field_agent1_id == assigned_field_agent2_id
      errors.add(:assigned_field_agent2_id, 'is same as Field Agent 1')
    end
  end

  def set_booking_as_scheduled
    booking.update_column(:status, :scheduled) unless booking.status_scheduled?
  end

  def set_ecb_install_code
    return unless type == 'Installation'

    counter = survey.installations.count + 1
    self.ecb_install_code = "I-#{survey.ecb_survey_code.gsub('S-', '')}"

    loop do
      break unless self.class.exists?(ecb_install_code:)
      self.ecb_install_code = "I#{counter}-#{survey.ecb_survey_code.gsub('S-', '')}"
    end
  end

  def notify_customer
    if saved_change_to_attribute?(:status) && status_scheduled?
      InstallationMailer.installation_scheduled(self).deliver_later
    end
    if saved_change_to_attribute?(:status) && status_cancelled?
      InstallationMailer.installation_cancelled(self).deliver_later
    end
    if saved_change_to_attribute?(:scheduled_install_date_time)
      InstallationMailer.installation_scheduled(self).deliver_later
    end
  end
end
