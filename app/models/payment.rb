class Payment < ApplicationRecord
  belongs_to :payable, polymorphic: true

  MODEL_TYPES = %w[Payment Refund].freeze

  validates :type, inclusion: { in: MODEL_TYPES }
  validates_presence_of :payment_ref_number, :payment_date, :payment_amount
  validates :payment_description, length: { maximum: 100 }

  default_scope { where(type: 'Payment') }

  def payment_report_description
    payment_rrn.present? ? "'#{payment_rrn}" : payment_description
  end
end
