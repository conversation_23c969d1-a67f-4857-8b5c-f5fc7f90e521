class PartnerLocation < ApplicationRecord
  belongs_to :partner
  belongs_to :location

  validates :location_id, uniqueness: { scope: :partner_id }

  validate :correct_location_type

  def correct_location_type
    return if location.location_type_city?

    errors.add(:location, '- Incorrect location selected for Partner')
  end

  def title
    "#{partner.name} - #{location.name}"
  end

  rails_admin do
    visible false
  end
end
