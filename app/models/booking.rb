class Booking < ApplicationRecord
  belongs_to :brand
  belongs_to :brand_dealer, optional: true
  belongs_to :brand_model, optional: true
  before_save :set_city_manager_email, if: :assigned_city_manager_id_changed?

  attr_accessor :skip_validations_callback

  GST_PERCENTAGE = 18

  # has_many :assignments, as: :assignable, class_name: 'Assignment', dependent: :destroy

  has_many :customer_connect_logs, as: :loggable, class_name: 'CustomerConnectLog', dependent: :destroy
  has_many :surveys, class_name: 'Survey', dependent: :destroy
  has_many :installations, class_name: 'Installation', dependent: :destroy
  has_many :complaints, class_name: 'Complaint', dependent: :destroy
  has_many :visits, class_name: 'Visit', dependent: :destroy
  has_many :payments, as: :payable, class_name: 'Payment', dependent: :destroy
  has_many :refunds, as: :payable, class_name: 'Refund', dependent: :destroy

  MODEL_TYPES = %w[Booking Survey Installation Complaint Visit].freeze
  validates :type, inclusion: { in: MODEL_TYPES }

  has_one_attached :customer_sign
  has_one_attached :ecb_sign
  has_one_attached :agent_sign
  has_one_attached :zip_file

  validates :brand_booking_code, allow_blank: true, length: { minimum: 2, maximum: 50 },
                                 uniqueness: { case_sensitive: false, scope: %i[brand_id] },
                                 on: :create, if: -> { type == 'Booking' }

  validates :dealer_name, presence: true, length: { minimum: 3, maximum: 200 }
  validates :dealer_location, allow_blank: true, length: { minimum: 2, maximum: 50 }
  validates :model_colour, allow_blank: true, length: { minimum: 2, maximum: 50 }
  validates :model_description, allow_blank: true, length: { minimum: 2, maximum: 100 }
  validates :model_variant, allow_blank: true, length: { minimum: 1, maximum: 100 }
  validates :model_int_color, allow_blank: true, length: { minimum: 2, maximum: 50 }
  validates :model_ext_color, allow_blank: true, length: { minimum: 2, maximum: 50 }

  validates :customer_name, presence: true, length: { minimum: 3, maximum: 100 }
  validates :email, allow_blank: true, length: { minimum: 10, maximum: 50 }
  validates :contact_number, presence: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :alt_contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :booking_address, presence: true, length: { minimum: 1, maximum: 1024 }, unless: proc { |c|
 (c.is_a?(Complaint) || c.is_a?(Visit)) }
  validates :address_line1, allow_blank: true, length: { minimum: 1, maximum: 200 }
  validates :address_line2, allow_blank: true, length: { minimum: 1, maximum: 200 }
  validates :locality, allow_blank: true, length: { minimum: 3, maximum: 50 }
  validates :company_name, allow_blank: true, length: { minimum: 3, maximum: 50 }
  validates :gst, allow_blank: true, length: { minimum: 15, maximum: 15 }
  validates :dealer_gst, allow_blank: true, length: { minimum: 15, maximum: 15 }
  validates :site_poc_name, allow_blank: true, length: { minimum: 3, maximum: 50 }
  validates :site_poc_contact, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }

  belongs_to :city, class_name: 'Location'
  belongs_to :zone, class_name: 'Location'
  belongs_to :state, class_name: 'Location'
  validates :pincode, presence: true, numericality: true, length: { minimum: 6, maximum: 6 }
  # t.boolean :is_outstation, default: false
  validates :latitude, allow_blank: true, numericality: { greater_than_or_equal_to:  -90, less_than_or_equal_to:  90 }
  validates :longitude, allow_blank: true, numericality: { greater_than_or_equal_to: -180, less_than_or_equal_to: 180 }

  belongs_to :assigned_city_manager, class_name: 'User', optional: true
  belongs_to :assigned_field_agent1, class_name: 'User', optional: true
  belongs_to :assigned_field_agent2, class_name: 'User', optional: true
  belongs_to :assigned_partner_user, class_name: 'User', optional: true
  belongs_to :survey_partner, class_name: 'Partner', optional: true
  belongs_to :install_partner, class_name: 'Partner', optional: true
  belongs_to :visit_partner, class_name: 'Partner', optional: true

  enum status: {
    draft: 0, # default after import only for booking
    waiting_schedule: 1, # after approval
    scheduled: 2, # after survey creation
    in_progress: 3,
    cancelled: 4,
    completed: 5,
    on_hold: 6,
    pending_approval: 7,
    assigned: 8, # after first survey scheduled
    closed: 9
  }, _prefix: true

  # booking states: draft -> pending_approval -> waiting_schedule -> scheduled, cancelled

  enum brand_booking_status: {
    booked: 0,
    invoiced: 1,
    delivered: 2,
    intend_to_cancel: 3,
    cancelled: 4
  }, _prefix: true

  enum sow_status: {
    completed: 0,
    pending: 1,
    na: 2
  }, _prefix: true

  enum extra_cable_payment_status: {
    completed: 0,
    pending: 1,
    na: 2
  }, _prefix: true

  scope :in_progress, -> { where(status: :in_progress) }
  scope :draft, -> { where(status: :draft) }
  scope :unassigned, -> { where(status: %i[draft waiting_schedule]) }
  scope :paid, -> { where(payment_received: true, status: :completed) }
  scope :unpaid, -> { where(payment_received: false, status: :completed) }

  scope :bookings, -> { where(type: 'Booking') }

  scope :completed, -> { where(status: :completed) }
  scope :cancelled, -> { where(status: :cancelled) }
  # scope :with_sendbird_channel, -> { where.not(sendbird_channel: nil) }
  scope :created_between, lambda { |start_date, end_date|
                            where(created_at: (start_date.to_date.beginning_of_day)..(end_date.to_date.end_of_day))
                          }
  scope :updated_between, lambda { |start_date, end_date|
                            where(updated_at: (start_date.to_date.beginning_of_day)..(end_date.to_date.end_of_day))
                          }
  scope :created_updated_not_equal, -> { where.not('created_at = updated_at') }

  scope :with_locations, -> { includes(:city, :state, :zone) }
  scope :with_brands, -> { includes(:brand, :brand_model) }
  scope :with_assignments, lambda {
                             includes(:assigned_city_manager, :assigned_field_agent1, :assigned_field_agent2, :assigned_partner_user)
                           }
  scope :with_partners, -> { includes(:survey_partner, :install_partner) }
  scope :with_database_report_association, lambda {
    includes(
      :customer_connect_logs,
      :assigned_city_manager,
      :city,
      :state,
      :zone,
      :uploaded_by,
      :brand_model,
      :brand,
      installations: %i[
        customer_connect_logs
        install_partner
        assigned_city_manager
        city
        state
        zone
        uploaded_by
      ],
      surveys: [
        :customer_connect_logs,
        :survey_partner,
        :assigned_city_manager,
        :city,
        :state,
        :zone,
        :uploaded_by,
        {installations: %i[
          customer_connect_logs
          install_partner
          assigned_city_manager
          city
          state
          zone
          uploaded_by
        ]}
      ]
    )
  }
  scope :with_invoicing_survey_report_association, lambda {
    includes(:booking, :zone, :state, :assigned_city_manager, :survey_partner, :installations, :brand)
  }
  scope :with_invoicing_installation_report_association, lambda {
    includes(:survey_partner, :install_partner)
  }
  scope :with_payment_tracker_report_association, lambda {
    includes(:installations, :assigned_city_manager, :city, :brand, :zone, :payments, :refunds)
  }

  default_scope { order(created_at: :desc) }

  ON_HOLD_REASONS = %w[CustomerUnreachable ChargerFaulty ItemMissing CustomerRequest
                       DealerRequest OtherReason].freeze
  CANCEL_REASONS = %w[CustomerUnreachable CustomerRefused Duplicate DealerRequest ProblemResolvedOverPhone DuplicateComplaintUploaded IncorrectUpload NoShow
                      OtherReason].freeze

  CLOSE_REASONS = %w[BurntWireReplaced ChargerToBeReplaced EVSEOEMNeedsToInspectTheCharger EmergencyButtonWasPressedNowRectified
                       EarthingValueToBeRectifiedByTheCustomer FaultCodesWereRemovedAndNowOk MCBWasTrippedNowOk MCBReplaced
                       NewEarthingToBeDoneByCustomer NewRFIDToBeObtainedByCustomerFromConcernedSupplier NewChargerKeyToBeObtainedByCustomerFromConcerned
                       PhaseVoltagesToBeRectifiedByDISCOM RCBOReplaced RedLightIssueResolved RCBOWasTrippedNowOk SoftwareUpdationDoneChargerIsWorkingNow
                       OthersReasons].freeze

  validates :on_hold_reason, inclusion: { in: ON_HOLD_REASONS }, if: :status_on_hold?
  validates :cancel_reason, inclusion: { in: CANCEL_REASONS }, if: :status_cancelled?
  validates :close_reason, inclusion: { in: CLOSE_REASONS }, if: :status_closed?

  validates :cancelled_at, presence: true, if: :status_cancelled?

  # t.datetime :first_contact_at
  # t.datetime :next_follow_up_at
  # t.datetime :survey_report_sent_at
  # t.datetime :approved_at

  validates :remarks, allow_blank: true, length: { minimum: 5, maximum: 1024 }
  validates :payment_remarks, allow_blank: true, length: { maximum: 100 }

  belongs_to :approved_by, class_name: 'User', optional: true
  belongs_to :uploaded_by, class_name: 'User', optional: true
  # t.datetime :layout_approved_at
  belongs_to :layout_approved_by, class_name: 'User', optional: true
  # t.date :charger_issued_date

  # CHARGER_CAPACITIES = ['3.3 KW', '7.5 KW', '11 KW', '22 KW'].freeze
  # validates :charger_capacity, allow_blank: true, inclusion: { in: CHARGER_CAPACITIES }

  # CHARGER_MAKE_TYPES = ['Exion', 'Company 2', 'Company 3', 'Other'].freeze
  # validates :charger_make_type, allow_blank: true, inclusion: { in: CHARGER_MAKE_TYPES }

  # validates :cable_length, allow_blank: true, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 500 }
  # validates :sanctioned_load, allow_blank: true,
  #                             numericality: { less_than_or_equal_to: 500 }

  validates :vdc, allow_blank: true, length: { minimum: 2, maximum: 20 }
  validates :flag, allow_blank: true, length: { minimum: 2, maximum: 20 }
  validates :mdi, allow_blank: true, length: { minimum: 2, maximum: 20 }
  validates :vin, allow_blank: true, length: { minimum: 2, maximum: 20 }
  validates :engine_number, allow_blank: true, length: { minimum: 2, maximum: 20 }

  # t.boolean :extra_cable_customer_pay, default: false

  enum install_type: {
    installation_complete: 0,
    installation_pending: 1,
    reinstall: 2,
    uninstall: 3,
    replacement: 4
  }, _prefix: true

  enum nature_of_work: {
    new_installation: 0,
    new_installation_second_charger: 1,
    reinstallation: 2
  }, _prefix: true

  # t.boolean :survey_done_same_day, default: false

  validates :cable_gauge, allow_blank: true, numericality: { greater_than_or_equal_to: 1, less_than_or_equal_to: 500 }

  validates :summary_stage1, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :summary_stage2, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :agent_notes, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :note_for_agent, allow_blank: true, length: { minimum: 3, maximum: 1024 }

  # t.boolean :customer_billing, default: false
  # t.datetime :customer_signed_at

  validates :customer_rating, allow_blank: true, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 5 }
  validates :customer_feedback, allow_blank: true, length: { minimum: 1, maximum: 1024 }
  validates :ecb_booking_code, allow_blank: true, uniqueness: { case_sensitive: false }, if: :type_is_booking?,
                               on: :create

  validate :correct_city_location_type, unless: :skip_validations_callback
  validate :correct_zone_location_type, unless: :skip_validations_callback
  validate :correct_state_location_type, unless: :skip_validations_callback

  before_validation :set_ecb_booking_code, on: :create

  after_save :send_notifications, unless: :skip_validations_callback
  after_commit :set_media_links, unless: :skip_validations_callback, if: :status_completed?
  # after_save :create_sendbird_channel, unless: :skip_validations_callback
  # after_save :update_chat_members, unless: :skip_validations_callback

  def status_text
    if approved_at && status_in_progress?
      'approved'
    else
      status
    end
  end

  def users_for_notifications
    [assigned_city_manager_id, assigned_field_agent1_id, assigned_field_agent2_id,
     assigned_partner_user_id, zonal_managers&.pluck(:id)].flatten.compact.uniq
  end

  def correct_city_location_type
    return if city&.location_type_city?

    errors.add(:city, '- Incorrect location selected for city')
  end

  def correct_zone_location_type
    return if zone.nil? || zone.location_type_zone?

    errors.add(:zone, '- Incorrect location selected for zone')
  end

  def correct_state_location_type
    return if state.nil? || state.location_type_state?

    errors.add(:state, '- Incorrect location selected for state')
  end

  def finished_survey
    surveys.where(status: :completed).first
  end

  def finished_installation
    installations.where(status: :completed).first
  end

  def first_survey
    surveys.first
  end

  def first_installation
    installations.first
  end

  def first_visit
    visits.first
  end

  rails_admin do
    exclude_fields :type, :ecb_survey_code, :brand_dealer, :brand_dealer_id, :ecb_install_code, :scheduled_survey_date_time, :scheduled_install_date_time,
                   :actual_survey_date_time, :actual_install_date_time, :actual_visit_date_time, :survey_completed_at, :install_completed_at, :assigned_field_agent1,
                   :assigned_field_agent2, :assigned_partner_user, :survey_partner, :install_partner, :booking, :survey, :customer_install_confirmed,
                   :customer_survey_confirmed, :on_hold_reason, :cancelled_at, :customer_ok_with_recording, :survey_report_sent_at, :approved_at,
                   :approved_by, :layout_approved_at, :layout_approved_by, :charger_issued_date, :extra_cable_customer_pay, :survey_done_same_day,
                   :charger_capacity, :charger_make_type, :cable_length, :cable_length_new, :sanctioned_load, :vdc, :flag, :mdi, :vin, :engine_number, :cable_gauge,
                   :install_type, :summary_stage1, :summary_stage2, :agent_notes, :note_for_agent, :customer_billing, :customer_signed_at,
                   :customer_rating, :customer_feedback, :booking_id, :survey_id, :uploaded_by, :surveys, :installations, :customer_connect_logs
  end

  def title
    "#{ecb_booking_code} - #{customer_name} - #{contact_number}"
  end

  def zonal_managers
    User.zonal_managers(zone_id) if zone_id.present?
  end

  def code
    case type
    when 'Booking'
      ecb_booking_code
    when 'Survey'
      ecb_survey_code
    when 'Installation'
      ecb_install_code
    when 'Complaint'
      ecb_complaint_code
    when 'Visit'
      ecb_visit_code
    end
  end

  def completed_at
    case type
    when 'Booking'
      nil
    when 'Survey'
      survey_completed_at
    when 'Installation'
      install_completed_at
    when 'Complaint'
      nil
    when 'Visit'
      visit_completed_at
    end
  end

  def pending_amount
    0.0
  end

  # def users_for_chat
  #   [assigned_city_manager_id, assigned_field_agent1_id, assigned_field_agent2_id,
  #    assigned_partner_user_id, zonal_managers&.pluck(:id), User.city_managers(city_id).pluck(:id),
  #    User.mis_users.pluck(:id), User.management_users.pluck(:id), User.super_users.pluck(:id),
  #    User.tech_users.pluck(:id)].flatten.compact.uniq
  # end

  # def create_sendbird_channel(update = false)
  #   return if type == 'Booking' || !assigned_city_manager || (sendbird_channel.present? && !update)

  #   user_ids = User.where(id: users_for_chat).pluck(:mobile).compact
  #   payload = {
  #     name: code,
  #     user_ids:,
  #     operator_ids: [assigned_city_manager.mobile],
  #     sendbird_channel:,
  #     metadata: {
  #       code:,
  #       type:,
  #       customer_name:,
  #       contact_number:,
  #       city_id: city_id.to_s,
  #       id: id.to_s
  #     },
  #     data: {
  #       code:,
  #       type:,
  #       customer_name:,
  #       contact_number:,
  #       city_id: city_id.to_s,
  #       id: id.to_s
  #     }
  #   }
  #   channel_url = Sendbird::Client.new.create_channel(payload)
  #   update_column(:sendbird_channel, channel_url) if channel_url.present?
  # end

  # def remove_users_from_chat(user_ids)
  #   return if type == 'Booking' || sendbird_channel.blank? || user_ids.empty?

  #   Sendbird::Client.new.remove_users_from_channel(sendbird_channel, user_ids)
  # end

  def process_and_create_zip_file
    # Tmp folder to store the download files from S3
    tmp_folder = "tmp/archive_#{code}_#{Time.now.to_i}"
    # Create a tmp folder if not exists
    FileUtils.mkdir_p(tmp_folder) unless Dir.exist?(tmp_folder)

    # download files from S3
    download_file_from_s3(customer_sign, tmp_folder, 'customer_sign.jpg') if customer_sign.present?
    download_file_from_s3(ecb_sign, tmp_folder, 'ecb_sign.jpg') if ecb_sign.present?
    download_file_from_s3(agent_sign, tmp_folder, 'agent_sign.jpg') if agent_sign.present?

    form_submissions.each do |form_submission|
      title = form_submission.form_question.title[0, 20].gsub(/[^0-9a-z]/i, '')
      counter = 1
      form_submission.images.each do |attachment|
        filename = "#{title}_#{counter}#{File.extname(attachment.blob.filename.to_s)}"
        download_file_from_s3(attachment, tmp_folder, filename)
        counter += 1
      end
      form_submission.videos.each do |attachment|
        filename = "#{title}_#{counter}#{File.extname(attachment.blob.filename.to_s)}"
        download_file_from_s3(attachment, tmp_folder, filename)
        counter += 1
      end
    end

    #---------- Convert to .zip --------------------------------------- #
    zip_filename = "#{tmp_folder}.zip"
    create_zip_from_tmp_folder(tmp_folder, zip_filename)

    # upoad zip file to S3
    skip_validations_callback = true
    zip_file.attach(io: File.open(zip_filename), filename: "#{code}.zip")

    # Remove files after download
    ZipFileCleanupJob.set(wait: 10.minute).perform_later(tmp_folder, zip_filename)

    zip_filename
  end

  def customer_connect_logs_remarks
    remarks = []
    customer_connect_logs.each do |log|
      remarks << [log.created_at.to_i, log.log_type, "#{log.created_at.strftime('%d-%m-%Y')} - #{log.summary}\x0A"]
    end
    remarks
  end

  def customer_connect_logs_comment
    customer_connect_logs.last&.log_type&.capitalize&.gsub('_', ' ')
  end

  # def latest_comment
  #   customer_connect_logs.order(created_at: :desc).first&.log_type&.capitalize&.gsub('_', ' ')
  # end

  def commissioning_string
    if install_type_installation_pending?
      'No'
    else
      'Yes'
    end
  end

  def install_type_string
    case install_type
    when 'installation_complete'
      'Type 1'
    when 'installation_pending'
      'Type 2'
    when 'reinstall'
      'Reinstall'
    when 'uninstall'
      'Uninstall'
    when 'replacement'
      'Replacement'
    end
  end

  def nature_of_work_string
    case nature_of_work
    when 'new_installation'
      'New Installation'
    when 'new_installation_second_charger'
      'New Installation (2nd Charger)'
    when 'reinstallation'
      'Reinstallation'
    end
  end

  # def self.valid_expired_sendbird_bookings(from, to)
  #   completed.with_sendbird_channel
  #            .where(
  #              '(survey_completed_at BETWEEN :from AND :to AND type = :survey_type) OR (install_completed_at BETWEEN :from AND :to AND type = :install_type)',
  #              from:,
  #              to:,
  #              survey_type: 'Survey',
  #              install_type: 'Installation'
  #            ).or(cancelled.with_sendbird_channel
  #   .where(
  #     'cancelled_at BETWEEN :from AND :to AND type != :booking_type',
  #     from:,
  #     to:,
  #     booking_type: 'Booking'
  #   ))
  # end

  def send_follow_up_notification
    meta = { type:, id:, code: }
    user_ids = [assigned_city_manager_id]
    payload = { title: 'Next follow Up', body: "#{type} #{code} has next follow up date today", meta: }
    return unless user_ids.present?

    messenger = PushMessenger::Gcm.new
    tokens = UserFcmRegistration.where(user_id: user_ids).pluck(:fcm_registration_token).compact
    return if tokens.blank? || tokens.count.zero?

    messenger.deliver(tokens, payload)
  end

  def previous_visit_date_time
    return unless type == 'Visit'

    return unless complaint.visits.count > 1
      complaint.visits.where.not(id:).order(actual_visit_date_time: :desc).first.actual_visit_date_time

  end

  def invoice_total
    @invoice_total ||= base_amount + gst_amount
  end

  def base_amount
    @base_amount ||= [cable_length_new.to_f - complementary_cable_length.to_f, 0].max * pricing_per_meter.to_f
  end

  def gst_amount
    @gst_amount ||= base_amount * GST_PERCENTAGE.to_f / 100
  end

  def amount_to_receive
    @amount_to_receive ||= [invoice_total - total_payment_amount + total_refund_amount, 0].max
  end

  def refundable_amount
    @amount_to_refund ||= [total_payment_amount - invoice_total - total_refund_amount, 0].max
  end

  def amount_received
    @amount_received ||= total_payment_amount - total_refund_amount
  end

  def amount_to_refund
    @amount_to_refund ||= refundable_amount
  end

  def total_payment_amount
    @total_payment_amount ||= payments.pluck(:payment_amount).sum
  end

  def total_refund_amount
    @total_refund_amount ||= refunds.pluck(:payment_amount).sum
  end

  def payment_count
    payments.count
  end

  def refund_count
    refunds.count
  end

  def first_payment
    payments.first
  end

  def second_payment
    payments.second
  end

  def third_payment
    payments.third
  end

  def first_refund
    refunds.first
  end

  def charger_size
    (obc || '').strip.split(' ').join.downcase
  end

  def charger_type
    @charger_type ||= brand&.charger_types&.find_by(charger_size:)
  end

  def should_preview_template?
    %w[Survey Installation].include?(type) && status_completed? && charger_type.present?
  end

  def seek_payment_email_preview
    return unless should_preview_template?

    email_preview_with_values(charger_type.seek_payment_email_template)
  end

  def refund_email_preview
    return unless should_preview_template?

    email_preview_with_values(charger_type.refund_email_template)
  end

  def refund_processed_email_preview
    return unless should_preview_template?

    email_preview_with_values(charger_type.refund_processed_email_template)
  end

  def email_preview_with_values(preview_template)
    return '' unless preview_template.present?
    return '' unless %w[Survey Installation].include?(type)

    preview = preview_template

    preview = preview.gsub('CUSTOMER_NAME', customer_name.to_s) if preview.include?('CUSTOMER_NAME')
    preview = preview.gsub('SURVEY_CODE', ecb_survey_code.to_s) if preview.include?('SURVEY_CODE')
    if preview.include?('SURVEY_DATE')
      preview = preview.gsub('SURVEY_DATE',
actual_survey_date_time.present? ? actual_survey_date_time.to_ist_format.strftime('%d-%b-%Y') : '')
    end
    if preview.include?('CABLE_LENGTH_DURING_SURVEY')
      cable_length_during_survey = type.eql?('Installation') ? survey&.cable_length_new&.to_s : cable_length_new&.to_s
      preview = preview.gsub('CABLE_LENGTH_DURING_SURVEY', cable_length_during_survey.to_s)
    end
    if preview.include?('CABLE_LENGTH_DURING_INSTALLATION')
      cable_length_during_installation = type.eql?('Installation') ? cable_length_new&.to_s : ''
      preview = preview.gsub('CABLE_LENGTH_DURING_INSTALLATION', cable_length_during_installation.to_s)
    end
    if preview.include?('AMOUNT_TO_BE_PAID_BY_CUSTOMER')
      preview = preview.gsub('AMOUNT_TO_BE_PAID_BY_CUSTOMER', number_to_indian_currency(amount_to_receive))
    end
    if preview.include?('TOTAL_BASE_AMOUNT')
      preview = preview.gsub('TOTAL_BASE_AMOUNT', number_to_indian_currency(base_amount))
    end
    if preview.include?('PRICING_END_DATE')
      pricing_end_date = brand_model&.current_pricing(charger_size)&.end_date
      preview = preview.gsub('PRICING_END_DATE',
pricing_end_date.present? ? pricing_end_date.to_ist_format.strftime('%d-%b-%Y') : '')
    end
    if preview.include?('EXTRA_CABLE_LENGTH_USED_DURING_SURVEY')
      preview = preview.gsub('EXTRA_CABLE_LENGTH_USED_DURING_SURVEY',
type.eql?('Installation') ? survey&.extra_cable_length.to_s : extra_cable_length.to_s)
    end
    if preview.include?('UNUSED_CABLE_LENGTH_AFTER_INSTALLATION')
      preview = preview.gsub('UNUSED_CABLE_LENGTH_AFTER_INSTALLATION',
type.eql?('Installation') && extra_cable_length.negative? ? extra_cable_length.abs.to_s : '0')
    end
    if preview.include?('AMOUNT_PAID_BY_CUSTOMER')
      preview = preview.gsub('AMOUNT_PAID_BY_CUSTOMER',
number_to_indian_currency(type.eql?('Installation') ? survey&.total_payment_amount + total_payment_amount : total_payment_amount))
    end
    preview = preview.gsub('BRAND_MODEL_NAME', brand_model&.name.to_s) if preview.include?('BRAND_MODEL_NAME')

    if preview.include?('REFUND_BASE_AMOUNT')
      preview = preview.gsub('REFUND_BASE_AMOUNT', number_to_indian_currency(amount_to_refund / 1.18))
    end
    if preview.include?('REFUND_AMOUNT_WITH_GST')
      preview = preview.gsub('REFUND_AMOUNT_WITH_GST', number_to_indian_currency(amount_to_refund))
    end
    if preview.include?('TOTAL_REFUNDED_AMOUNT')
      preview = preview.gsub('TOTAL_REFUNDED_AMOUNT',
number_to_indian_currency(type.eql?('Installation') ? survey&.total_refund_amount + total_refund_amount : total_refund_amount))
    end
    # For refund
    if preview.include?('PAYMENT_REF_NUMBER')
      refund_payment_ref_number = refunds.present? ? refunds.last.payment_ref_number : ''
      preview = preview.gsub('PAYMENT_REF_NUMBER', refund_payment_ref_number)
    end

    preview
  end

  def pricing_expired
    !brand_model&.current_pricing(charger_size).present?
  end

  def send_payment_email(action_type)
    PaymentMailer.payment_action(self, action_type, assigned_city_manager).deliver_later
  end

  private

  def download_file_from_s3(attachment, tmp_folder, filename)
    File.open(File.join(tmp_folder, filename), 'wb') do |file|
      attachment.download { |chunk| file.write(chunk) }
    end
  end

  def create_zip_from_tmp_folder(tmp_folder, filename)
    Zip::File.open(filename, Zip::File::CREATE) do |zip_file|
      Dir[File.join(tmp_folder, '**', '**')].each do |file|
        zip_file.add(file.sub("#{tmp_folder}/", ''), file)
      end
    end
  end

  # def update_chat_members
  #   return if type == 'Booking' || sendbird_channel.blank?

  #   # only if city manager changed or field agent changed or partner manager changed
  #   return unless saved_change_to_attribute?(:assigned_city_manager_id) ||
  #                 saved_change_to_attribute?(:assigned_field_agent1_id) ||
  #                 saved_change_to_attribute?(:assigned_field_agent2_id) ||
  #                 saved_change_to_attribute?(:assigned_partner_user_id)

  #   user_ids = User.where(id: users_for_chat).pluck(:mobile).compact
  #   Sendbird::Client.new.add_users_to_channel(sendbird_channel, user_ids)

  #   # users no longer needed for chat
  #   user_ids_to_remove = User.where(id: users_to_remove_from_chat).pluck(:mobile).compact
  #   user_ids = user_ids_to_remove - user_ids
  #   remove_users_from_chat(user_ids)
  # end

  def send_notifications
    payload = {}
    meta = { type:, id:, code: }

    if type == 'Booking'
      if id_changed? || saved_change_to_attribute?(:id) || created_at == updated_at && assigned_city_manager_id.present?
        payload = { title: "New #{type} Assigned", body: "New #{type} Assigned - #{code}", meta: }
        messenger = PushMessenger::Gcm.new
        user_ids = [assigned_city_manager_id]
        tokens = UserFcmRegistration.where(user_id: user_ids).pluck(:fcm_registration_token).compact
        return if tokens.blank? || tokens.count.zero?

        messenger.deliver(tokens, payload)
      end
      return
    end

    ZipFileCreateJob.set(wait: 10.seconds).perform_later(id) if saved_change_to_attribute?(:status) && (status_completed? || status_pending_approval?)

    if id_changed? || saved_change_to_attribute?(:id) || created_at == updated_at
      payload = { title: "New #{type} Assigned", body: "New #{type} Assigned - #{code}", meta: }
    elsif saved_change_to_attribute?(:actual_survey_date_time) || saved_change_to_attribute?(:actual_install_date_time)
      payload = { title: "#{type} work started", body: "#{type} #{code} work started", meta: }
    elsif saved_change_to_attribute?(:scheduled_survey_date_time) || saved_change_to_attribute?(:scheduled_install_date_time)
      payload = { title: "#{type} rescheduled", body: "#{type} #{code} rescheduled", meta: }
    elsif saved_change_to_attribute?(:survey_completed_at) || saved_change_to_attribute?(:install_completed_at)
      payload = { title: "#{type} work completed", body: "#{type} #{code} work completed ", meta: }
    elsif saved_change_to_attribute?(:status) && status_on_hold?
      payload = { title: "#{type} put on hold",
                  body: "#{type} #{code} put on hold for reason: #{on_hold_reason}", meta: }
    elsif saved_change_to_attribute?(:status) && status_cancelled?
      payload = { title: "#{type} cancelled", body: "#{type} #{code} cancelled for reason: #{cancel_reason}",
                  meta: }
    elsif saved_change_to_attribute?(:status) && status_closed?
      payload = { title: "#{type} closed", body: "#{type} #{code} closed for reason: #{close_reason}",
                  meta: }
    elsif saved_change_to_attribute?(:status) && status_pending_approval?
      payload = { title: "#{type} waiting for approval", body: "#{type} #{code} is waiting for approval", meta: }
    elsif saved_change_to_attribute?(:status) && status_in_progress? && saved_change_to_attribute?(:approved_at)
      payload = { title: "#{type} approved", body: "#{type} #{code} is approved", meta: }
    elsif saved_change_to_attribute?(:assigned_city_manager_id)
      user_ids = [assigned_city_manager_id, zonal_managers&.pluck(:id), assigned_city_manager_id_was]
      payload = { title: "#{type} City Manager reassigned", body: "#{type} #{code} city manager reassigned",
                  meta: }
    elsif saved_change_to_attribute?(:assigned_field_agent1_id) || saved_change_to_attribute?(:assigned_field_agent2_id)
      payload = { title: "#{type} Field Agent reassigned", body: "#{type} #{code} field agent reassigned",
                  meta: }
    elsif saved_change_to_attribute?(:assigned_partner_user_id)
      payload = { title: "#{type} Partner Manager reassigned", body: "#{type} #{code} partner manager reassigned",
                  meta: }
    elsif saved_change_to_attribute?(:remarks) || saved_change_to_attribute?(:summary_stage1) ||
          saved_change_to_attribute?(:summary_stage2) || saved_change_to_attribute?(:note_for_agent) ||
          saved_change_to_attribute?(:agent_notes)
      payload = { title: "#{type} remarks updated", body: "#{type} #{code} remarks updated", meta: }
    elsif saved_change_to_attribute?(:status)
      payload = { title: "#{type} status changed", body: "#{type} status changed for #{code}", meta: }
    elsif saved_change_to_attribute?(:customer_rating) && customer_rating && customer_rating < 4
      user_ids = [assigned_city_manager_id, zonal_managers&.pluck(:id)]
      payload = { title: "Low Rating Alert (#{customer_rating}/5)", body: "#{type} #{code} received low customer rating",
                  meta: }
    end
    if payload[:title].present?
      messenger = PushMessenger::Gcm.new
      user_ids = users_for_notifications if user_ids.blank?
      tokens = UserFcmRegistration.where(user_id: user_ids).pluck(:fcm_registration_token).compact
      return if tokens.blank? || tokens.count.zero?

      messenger.deliver(tokens, payload)
    end
  rescue Exception => e
    Rails.logger.debug e
  end

  def set_ecb_booking_code
    return unless type == 'Booking'

    model_code = brand_model ? brand_model.code : '00'
    today = Time.now.in_time_zone('Kolkata').to_date

    prefix = "B-#{brand.code}#{model_code}#{today.strftime('%y%b')}".upcase

    running_count = Booking.where("type = 'Booking' AND ecb_booking_code LIKE ?",
                                  "#{prefix}%").order(ecb_booking_code: :desc).pluck(:ecb_booking_code).map { |code| code.gsub(prefix, '').to_i }.max

    counter = running_count || 0
    counter += 1

    self.ecb_booking_code = "#{prefix}#{counter}"

    loop do
      break unless self.class.exists?(ecb_booking_code:)

      counter += 1
      self.ecb_booking_code = "#{prefix}#{counter}"
    end
  end

  def users_to_remove_from_chat
    user_ids = []
    user_ids << assigned_city_manager_id_previously_was if saved_change_to_attribute?(:assigned_city_manager_id)
    user_ids << assigned_field_agent1_id_previously_was if saved_change_to_attribute?(:assigned_field_agent1_id)
    user_ids << assigned_field_agent2_id_previously_was if saved_change_to_attribute?(:assigned_field_agent2_id)
    user_ids << assigned_partner_user_id_previously_was if saved_change_to_attribute?(:assigned_partner_user_id)
    user_ids
  end

  def type_is_booking?
    type == 'Booking'
  end

  def set_media_links
    SetMediaLinksJob.perform_later(self)
  end

  def set_city_manager_email
    self.city_manager_email = User.find_by(id: assigned_city_manager_id)&.email
  end
end
