class BrandModelPricing < ApplicationRecord
  belongs_to :brand_model

  validates_presence_of :pricing_per_meter, :complementary_cable_length, :start_date, :end_date

  validates :pricing_per_meter, numericality: { greater_than_or_equal_to: 50, less_than_or_equal_to: 1000 }
  validates :complementary_cable_length, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 1000 }

  validate :start_date_is_today_or_future
  validate :end_date_is_after_or_equal_to_start_date
  validate :dates_do_not_overlap_with_existing_pricing

  enum charger_size: BrandModel.charger_sizes

  validates :charger_size, presence: true, inclusion: { in: charger_sizes.keys }, on: :create

  private

  def start_date_is_today_or_future
    if start_date.present? && start_date < Date.today
      errors.add(:start_date, "must be today or a later date")
    end
  end

  def end_date_is_after_or_equal_to_start_date
    if end_date.present? && start_date.present? && end_date < start_date
      errors.add(:end_date, "must be equal to or after the start date")
    end
  end

  def dates_do_not_overlap_with_existing_pricing
    if start_date.present? && end_date.present?
      overlapping_pricing = BrandModelPricing.where("brand_model_id = ? AND start_date <= ? AND end_date >= ?", brand_model_id, end_date, start_date).where(charger_size: charger_size)

      if id.present?
        overlapping_pricing = overlapping_pricing.where.not(id: id)
      end

      if overlapping_pricing.exists?
        errors.add(:base, "Pricing dates overlap with an existing pricing")
      end
    end
  end
end
