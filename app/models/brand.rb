class Brand < ApplicationRecord
  has_many :brand_models, dependent: :destroy
  has_many :charger_types, dependent: :destroy
  has_many :user_roles, dependent: :nullify
  validates :name, uniqueness: { case_sensitive: false }, presence: true, length: { minimum: 3, maximum: 30 }
  validates :code, uniqueness: { case_sensitive: false }, presence: true, length: { minimum: 2, maximum: 10 }
  has_one_attached :brand_logo do |attachable|
    attachable.variant :thumb, resize_to_limit: [100, 100]
  end
  has_many :form_section_brands, dependent: :destroy
  has_many :form_sections, through: :form_section_brands

  has_many :form_question_brands, dependent: :destroy
  has_many :form_questions, through: :form_question_brands
  has_one :user

  validates :contact_person, allow_blank: true, length: { minimum: 3, maximum: 30 }
  validates :billing_address, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :gst, allow_blank: true, length: { minimum: 15, maximum: 15 }
  validates :contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :alt_contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }

  after_create :assign_to_forms, if: :add_to_forms?

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  scope :active, -> { where(status: :active) }

  default_scope { order(:name) }

  rails_admin do
    exclude_fields :user_roles, :form_section_brands, :form_questions, :form_question_brands, :form_sections
  end

  private

  def assign_to_forms
    AddToFormsJob.perform_later(id)
  end
end
