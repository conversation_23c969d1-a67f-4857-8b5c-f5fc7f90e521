class PaymentDetailsImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :imported_count, :uploaded_by

  def initialize(payment_details_file = nil, current_user)
    @file = payment_details_file
    @uploaded_by = current_user
    @imported_count = 0
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    when '.xlsx'
      Roo::Spreadsheet.open(file.path, extension: :xlsx)
    else
      raise "Unknown File Type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    headers = spreadsheet.row(1).map(&:to_s)

    payment_details = []
    ecb_survey_codes = []

    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header row

      payment_data = Hash[[headers, row].transpose]

      if payment_data['SurveyRefNumber'].blank?
        errors.add :base, "Row #{idx + 1}: SurveyRefNumber can't be blank."
        next
      end

      if payment_data['PaymentDate'].blank?
        errors.add :base, "Row #{idx + 1}: Payment date is required."
        next
      end

      unless valid_date?(payment_data['PaymentDate'])
        errors.add :base, "Row #{idx + 1}: Invalid payment date format."
        next
      end

      if Date.parse(payment_data['PaymentDate'].to_s) > Date.today
        errors.add :base, "Row #{idx + 1}: Payment date cannot be in the future."
        next
      end

      if payment_data['PaymentRefNumber'].blank?
        errors.add :base, "Row #{idx + 1}: Payment Ref Number is required."
        next
      end

      if payment_data['PaymentAmount'].blank?
        errors.add :base, "Row #{idx + 1}: Payment Amount is required."
        next
      end

      detail = {
        ecb_survey_code:      payment_data['SurveyRefNumber'],
        payment_ref_number:   payment_data['PaymentRefNumber'],
        payment_date:         payment_data['PaymentDate'],
        payment_amount:       payment_data['PaymentAmount'],
        payment_party_code:   payment_data['PaymentPartyCode'],
        bank_name:            payment_data['BankName'],
        payment_gateway_id:   payment_data['PaymentGatewayID'],
        customer_bank:        payment_data['CustomerBank'],
        customer_vpa:         payment_data['CustomerVPA'],
        payment_description:  payment_data['PaymentDescription'],
        payment_rrn:          payment_data['PaymentRRN']
      }

      payment_details << { row_number: idx + 1, data: detail }
      ecb_survey_codes << detail[:ecb_survey_code]
    end

    # Find all surveys and map by ecb_survey_code
    surveys = Survey.where(ecb_survey_code: ecb_survey_codes.uniq)
                    .pluck(:ecb_survey_code, :id)
                    .to_h

    payments = []

    payment_details.each do |entry|
      row_number = entry[:row_number]
      detail = entry[:data]
      survey_id = surveys[detail[:ecb_survey_code]]

      unless survey_id
        errors.add :base, "Row #{row_number}: SurveyRefNumber '#{detail[:ecb_survey_code]}' not found."
        next
      end

      payment = Payment.new(
        payable_id:           survey_id,
        payable_type:         'Booking',
        payment_ref_number:   detail[:payment_ref_number],
        payment_date:         detail[:payment_date],
        payment_amount:       detail[:payment_amount],
        payment_party_code:   detail[:payment_party_code],
        bank_name:            detail[:bank_name],
        payment_gateway_id:   detail[:payment_gateway_id],
        customer_bank:        detail[:customer_bank],
        customer_vpa:         detail[:customer_vpa],
        payment_description:  detail[:payment_description],
        payment_rrn:          detail[:payment_rrn]
      )

      unless payment.valid?
        payment.errors.full_messages.each do |msg|
          errors.add :base, "Row #{row_number}: #{msg}"
        end
        next
      end

      payments << payment
    end

    payments
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    imported_items
    return false if errors.any?

    if imported_items.map(&:valid?).all?
      imported_items.each(&:save!)
      @imported_count = imported_items.count
      true
    else
      false
    end
  end

  def valid_date?(value)
    Date.parse(value.to_s)
    true
  rescue ArgumentError, TypeError
    false
  end

end
