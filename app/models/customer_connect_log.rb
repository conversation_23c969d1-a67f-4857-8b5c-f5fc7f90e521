class CustomerConnectLog < ApplicationRecord
  belongs_to :loggable, polymorphic: true
  belongs_to :user

  validates :summary, allow_blank: true, length: { minimum: 3, maximum: 500 }

  enum log_type: {
    not_picked: 0,
    switched_off: 1,
    scheduled: 2,
    customer_refused: 3,
    wants_survey_later: 4,
    any_other_reason: 5,
    outstation_approval_awaited: 6,
    cancelled_the_booking: 7,
    survey_installation_done_against_another_survey_booking_no: 8,
    survey_installation_done_under_other_dealer: 9,
    survey_installation_done_against_other_charger_package: 10,
    visit_planned: 11,
    vehicle_is_not_available: 12,
    problem_resolved_over_phone: 13,
    customer_not_available_and_asked_to_call_later: 14,
    customer_did_not_purchase_any_charger_package: 15,
    call_me_later: 16,
    cee_customer_ready_for_survey: 17,
    customer_scope_of_working_pending: 18,
    customer_wants_installation_later: 19,
    installation_already_done_by_another_partner: 20,
    noc_not_provided_by_society: 21,
    payment_for_extra_cable_is_pending: 22,
    site_house_under_construction: 23,
    site_is_good_to_go_for_installation: 24,
    customer_already_has_an_ev_charger_and_does_not_want_another_charger_installation: 25
  }, _prefix: true

  # after_save :send_notifications

  private

  def send_notifications
    messenger = PushMessenger::Gcm.new
    user_ids = loggable.users_for_notifications
    tokens = UserFcmRegistration.where(user_id: user_ids).pluck(:fcm_registration_token).compact
    return if tokens.blank? || tokens.count.zero?

    payload = { title: "New log added for #{loggable.type}",
                body: "New log added for #{loggable.type} : #{loggable.code}",
                meta: { type: loggable.type, id: loggable.id, code: loggable.code } }

    messenger.deliver(tokens, payload)
  end
end
