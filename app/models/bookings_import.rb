class BookingsImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :imported_count, :uploaded_by

  def initialize(bookings_file = nil, current_user)
    @file = bookings_file
    @uploaded_by = current_user
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    # when '.xls' then Roo::Spreadsheet.open(file, extension: :xls)
    when '.xlsx' then Roo::Spreadsheet.open(file, extension: :xlsx)
    else raise "Unknown file type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    count = spreadsheet.count
    headers = spreadsheet.row(1) # get header row
    booking_models = []
    booking_codes = []
    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header

      # create hash from headers and cells
      booking_data = Hash[[headers, row].transpose]
      valid_statuses = Booking.brand_booking_statuses.keys

      booking_code = booking_data['BrandBookingCode']
      if !booking_code.blank? && booking_codes.include?(booking_code)
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: Duplicate BrandBookingCode"
        next
      elsif !booking_code.blank?
        booking_codes << booking_code
      end

      brand = Brand.find_by(code: booking_data['BrandCode'], status: :active)
      if brand.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: BrandCode not found"
        next
      end

      if booking_data['BrandModelCode']
        brand_model = brand.brand_models.find_by(code: booking_data['BrandModelCode'], status: :active)
        if brand_model.nil?
          errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: BrandModelCode not found"
          next
        end
      end

      city_manager_user = User.find_by(mobile: booking_data['CityManagerMobile'], user_type: :ecb, status: :active)
      if city_manager_user.nil? || !city_manager_user.city_manager?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: CityManagerMobile not found"
        next
      end

      zone = Location.find_by(code: booking_data['ZoneCode'], location_type: :zone, status: :active)
      if zone.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: ZoneCode not found"
        next
      end

      city = Location.find_by(code: booking_data['EVCityCode'], location_type: :city, status: :active)
      if city.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: EVCityCode not found"
        next
      end

      state = Location.find_by(code: booking_data['CustomerStateCode'], location_type: :state, status: :active)
      if state.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: CustomerStateCode not found"
        next
      end

      brand_booking_status = booking_data['BrandBookingStatus'].strip.split.map(&:downcase).join("_")
      if !valid_statuses.include?(brand_booking_status)
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: Invalid Brand Booking Status"
        next
      end

      booking = Booking.new(brand:, brand_model:, assigned_city_manager: city_manager_user, zone:,
                            dealer_name: booking_data['DealerName'], dealer_location: booking_data['DealerLocation'], dealer_email: booking_data['DealerEmail'],
                            city_manager_email: booking_data['CityManagerEmail'], zonal_manager_email: booking_data['ZonalManagerEmail'],
                            brand_booking_code: booking_data['BrandBookingCode'], brand_booking_date: booking_data['BrandBookingDate'],
                            brand_booking_status: brand_booking_status,
                            model_variant: booking_data['ModelVariant'], model_colour: booking_data['ModelColour'],
                            model_description: booking_data['ModelDescription'], ecb_receive_date: booking_data['EcbReceiveDate'],
                            customer_name: booking_data['CustomerName'], contact_number: booking_data['CustomerPhone1'],
                            alt_contact_number: booking_data['CustomerPhone2'], email: booking_data['CustomerEmail'],
                            booking_address: booking_data['BookingAddress'], address_line1: booking_data['CustomerAddress1'],
                            address_line2: booking_data['CustomerAddress2'], city:, state:, vin: booking_data['VIN'],
                            pincode: booking_data['CustomerPincode'], remarks: booking_data['Remarks'],
                            obc: booking_data['OBC'], customer_city: booking_data['CustomerCity'], gst: booking_data['CustomerGST'],
                            dealer_gst: booking_data['DealerGST'], uploaded_by:)

      unless booking.valid?
        booking.errors.full_messages.each do |msg|
          errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: #{msg}"
        end
        next
      end
      booking_models << booking
    end
    booking_models
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    imported_items
    return if errors.any?

    if imported_items.map(&:valid?).all?
      imported_items.each(&:save!)
      @imported_count = imported_items.count
      true
    else
      false
    end
  end
end
