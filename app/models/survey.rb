class Survey < Booking
  validates :scheduled_survey_date_time, presence: true

  validates :assigned_city_manager_id, presence: true

  # t.datetime :actual_survey_date_time - on addition, validate if date is not in future
  # t.datetime :survey_completed_at - on addition, validate if date is not in future

  validate :correct_assignments, unless: :skip_validations_callback

  validates :survey_partner_id, presence: true
  # validates :assigned_partner_user_id, presence: true

  belongs_to :booking, class_name: 'Booking'
  has_many :customer_connect_logs, as: :loggable, class_name: 'CustomerConnectLog', dependent: :destroy

  has_many :form_submissions, as: :formable, dependent: :destroy
  has_many :installations, class_name: 'Installation', dependent: :destroy

  has_many :payments, as: :payable, class_name: 'Payment', dependent: :destroy
  has_many :refunds, as: :payable, class_name: 'Refund', dependent: :destroy

  before_validation :copy_parent_data, on: :create

  validates :ecb_survey_code, allow_blank: true, uniqueness: { case_sensitive: false }, if: lambda {
                                                                                              type == 'Survey'
                                                                                            }, on: :create

  before_validation :set_ecb_survey_code, on: :create
  after_create :set_booking_as_scheduled
  after_update :set_booking_as_assigned, if: :status_scheduled?
  before_update :change_status, if: :status_on_hold?
  before_update :add_pricing, if: :status_completed?
  after_commit :notify_customer, unless: :skip_validations_callback, if: -> { brand&.automated_emails_enabled? }

  def change_status
    return unless scheduled_survey_date_time_changed?

    Rails.logger.info('Setting as scheduled')
    self.status = :scheduled
  end

  def add_pricing
    return unless status_changed?

    pricing = brand_model&.current_pricing(charger_size)

    self.pricing_per_meter = pricing&.pricing_per_meter
    self.complementary_cable_length = pricing&.complementary_cable_length
  end

  # Survey states: scheduled -> in_progress -> on_hold -> pending_approval -> completed, cancelled

  rails_admin do
    exclude_fields :type, :brand_dealer, :brand_dealer_id, :brand_booking_date,
                   :ecb_receive_date, :ecb_booking_code, :ecb_install_code,
                   :scheduled_install_date_time, :actual_install_date_time, :install_completed_at, :install_partner, :survey,
                   :layout_approved_at, :layout_approved_by, :charger_issued_date, :extra_cable_customer_pay,
                   :vin, :engine_number, :install_type, :survey_id, :customer_install_confirmed, :uploaded_by,
                   :customer_connect_logs, :form_submissions, :installations
  end

  def title
    "#{ecb_survey_code} - #{customer_name} - #{contact_number}"
  end

  def base_amount
    @base_amount ||= [cable_length_new.to_f - complementary_cable_length.to_f, 0].max * pricing_per_meter.to_f
  end

  def gst_amount
    @gst_amount ||= base_amount * GST_PERCENTAGE.to_f / 100
  end

  def invoice_total
    @invoice_total ||= base_amount + gst_amount
  end

  def amount_to_refund
    @amount_to_refund ||= refundable_amount
  end

  def extra_cable_length
    if cable_length_new.to_f > complementary_cable_length.to_f
      cable_length_new.to_f - complementary_cable_length.to_f
    else
      0
    end
  end

  # def latest_comment
  #   CustomerConnectLog.where(loggable_id: [booking_id, id]).order(created_at: :desc).first&.log_type&.capitalize&.gsub('_', ' ')
  # end
  #

  def total_paid_amount
    @total_paid_amount ||= payments.pluck(:payment_amount).sum
  end

  def pending_amount
    (invoice_total - total_payment_amount).round(0)
  end

  def payment_link
    return unless pending_amount.positive?

    json = Setu::Client.new.generate_qr_via_lambda(pending_amount.to_i, ecb_survey_code,
                                                   "Payment for #{ecb_survey_code}")
    json['shortLink']
  end

  private

  def copy_parent_data
    return unless booking

    self.brand_id = booking.brand_id
    self.dealer_name = booking.dealer_name
    self.dealer_email = booking.dealer_email
    self.city_manager_email = booking.city_manager_email
    self.zonal_manager_email = booking.zonal_manager_email
    self.dealer_location = booking.dealer_location
    self.ecb_receive_date = booking.ecb_receive_date
    self.brand_booking_code = booking.brand_booking_code
    self.brand_booking_date = booking.brand_booking_date
    self.ecb_booking_code = booking.ecb_booking_code
    self.cee_name = booking.cee_name
    self.cee_contact_date = booking.cee_contact_date
    self.charger_issued_date = booking.charger_issued_date
  end

  def correct_assignments
    if assigned_city_manager_id && (!assigned_city_manager || !assigned_city_manager.status_active? || !assigned_city_manager.city_manager?)
      errors.add(:assigned_city_manager_id,
                 'is not active')
    end
    if survey_partner_id && (!survey_partner || !survey_partner.status_active?)
      errors.add(:survey_partner_id,
                 'is not active')
    end
    if assigned_field_agent1_id && (!assigned_field_agent1 || !assigned_field_agent1.status_active?)
      errors.add(:assigned_field_agent1_id,
                 'is not active')
    end
    if assigned_field_agent2_id && (!assigned_field_agent2 || !assigned_field_agent2.status_active?)
      errors.add(:assigned_field_agent2_id,
                 'is not active')
    end
    if assigned_partner_user_id && (!assigned_partner_user || !assigned_partner_user.status_active?)
      errors.add(:assigned_partner_user_id,
                 'is not active')
    end
    if assigned_field_agent1
      if survey_partner_id.blank? || assigned_field_agent1.partner_id != survey_partner_id
        errors.add(:assigned_field_agent1_id,
                   'does not belong to survey partner')
      end
      errors.add(:assigned_field_agent1_id, 'is not a field agent') unless assigned_field_agent1.field_agent?
    end
    if assigned_field_agent2
      if survey_partner_id.blank? || assigned_field_agent2.partner_id != survey_partner_id
        errors.add(:assigned_field_agent2_id,
                   'does not belong to survey partner')
      end
      errors.add(:assigned_field_agent2_id, 'is not a field agent') unless assigned_field_agent2.field_agent?
    end
    if assigned_partner_user
      if survey_partner_id.blank? || assigned_partner_user.partner_id != survey_partner_id
        errors.add(:assigned_partner_user_id,
                   'does not belong to survey partner')
      end
      errors.add(:assigned_partner_user_id, 'is not a partner manager') unless assigned_partner_user.partner_manager?
    end
    if assigned_field_agent1_id && assigned_field_agent2_id && assigned_field_agent1_id == assigned_field_agent2_id
      errors.add(:assigned_field_agent2_id, 'is same as Field Agent 1')
    end
  end

  def set_ecb_survey_code
    return unless type == 'Survey'

    model_code = brand_model ? brand_model.code : '00'
    today = Time.now.in_time_zone('Kolkata').to_date

    prefix = "S-#{brand.code}#{model_code}#{today.strftime('%y%b')}#{city.code}".upcase

    running_count = Survey.where('ecb_survey_code LIKE ?',
                                 "#{prefix}%").order(ecb_survey_code: :desc).first.try(:ecb_survey_code)

    counter = running_count ? running_count.gsub(prefix, '').to_i : 0
    counter += 1

    self.ecb_survey_code = "#{prefix}#{counter}"

    loop do
      break unless self.class.exists?(ecb_survey_code:)

      counter += 1
      self.ecb_survey_code = "#{prefix}#{counter}"
    end
  end

  def set_booking_as_scheduled
    booking.update_columns(status: :scheduled, customer_survey_confirmed: true) unless booking.status_scheduled?
  end

  def set_booking_as_assigned
    booking.update_column(:status, :assigned) unless booking.status_assigned?
  end

  def notify_customer
    SurveyMailer.survey_scheduled(self).deliver_later if saved_change_to_attribute?(:status) && status_scheduled?
    SurveyMailer.survey_cancelled(self).deliver_later if saved_change_to_attribute?(:status) && status_cancelled?
    SurveyMailer.survey_scheduled(self).deliver_later if saved_change_to_attribute?(:scheduled_survey_date_time)
    return unless saved_change_to_attribute?(:is_sow_completed)

    SurveyMailer.charger_delivered(self).deliver_later if charger_issued_date.present? && is_sow_completed.eql?(false)
  end
end
