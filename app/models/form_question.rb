class FormQuestion < ApplicationRecord
  belongs_to :form_section
  validates :title, presence: true, length: { minimum: 3, maximum: 500 }
  validates :description, allow_blank: true, length: { minimum: 3, maximum: 500 }
  validates :position, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  has_many :form_submissions, dependent: :nullify
  has_many :form_question_brands, dependent: :destroy
  accepts_nested_attributes_for :form_question_brands, allow_destroy: true

  # after_create :associate_brands

  enum question_type: {
    free_text: 0,
    single_choice: 1,
    multi_choice: 2,
    boolean: 3,
    numeric: 4
  }, _prefix: true

  enum image_upload: {
    no: 0,
    optional: 1,
    mandatory: 2
  }, _prefix: true

  enum video_upload: {
    no: 0,
    optional: 1,
    mandatory: 2
  }, _prefix: true

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  serialize :choices, Array

  scope :active, -> { where(status: :active) }
  scope :for_section, ->(section_id) { where(form_section_id: section_id) }

  default_scope { order(:position) }

  def choice_raw
    choices&.join(', ')
  end

  def choice_raw=(values)
    self.choices = values.split(',').map(&:strip) if values.is_a?(String)
  end

  def form_question_brands_attributes=(*args)
    associate_brands if args.blank? || args.empty? || args[0].blank? || args[0].empty?
    super(*args)
  end

  rails_admin do
    exclude_fields :form_submissions, :form_question_brands
  end

  private

  def associate_brands
    return unless form_question_brands.count.zero?

    Brand.active.each do |brand|
      form_question_brands.build brand_id: brand.id
      # joiner = form_question_brands.build brand_id: brand.id
      # joiner.save!
    end
  end
end
