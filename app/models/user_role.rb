class UserRole < ApplicationRecord
  belongs_to :user
  belongs_to :brand, optional: true
  belongs_to :partner, optional: true
  belongs_to :location, optional: true

  validates :role_type, presence: true
  validates :role_type, uniqueness: { scope: %i[user_id location_id brand_id partner_id] }

  validates_absence_of :brand, if: lambda {
    role_type_zonal_manager? || role_type_mis? || role_type_management? || role_type_super_user? || role_type_central_tech?
  }

  validates_absence_of :partner, if: lambda {
    !role_type_field_agent? && !role_type_partner_manager?
  }

  validates :partner, presence: true, if: lambda {
    (role_type_field_agent? || role_type_partner_manager?) && !user.user_type_ecb?
  }

  validates_absence_of :location, if: lambda {
    role_type_mis? || role_type_management? || role_type_super_user? || role_type_central_tech? || role_type_finance_controller?
  }

  validates :location, presence: true, if: lambda {
    !role_type_mis? && !role_type_management? && !role_type_super_user? && !role_type_central_tech? && !role_type_finance_controller?
  }

  validate :correct_location_type
  validate :correct_role_type

  enum role_type: {
    customer: 0,
    field_agent: 1,
    partner_manager: 2,
    city_manager: 3,
    zonal_manager: 4,
    mis: 5,
    management: 6,
    super_user: 7,
    central_tech: 8,
    finance_controller: 9
  }, _prefix: true

  def correct_role_type
    return if user.user_type_ecb? && !role_type_partner_manager? && !role_type_field_agent?
    return if user.user_type_external? && (role_type_partner_manager? || role_type_field_agent?)

    errors.add(:role_type, '- Incorrect role for user')
  end

  def correct_location_type
    return if location.nil?

    if role_type_zonal_manager?
      errors.add(:location, '- Incorrect location selected for Zonal Manager') unless location.location_type_zone?
    elsif !location.location_type_city?
      errors.add(:location, '- Incorrect location selected for User')
    end
  end

  def title
    "#{role_type.humanize} - #{location&.name} - #{brand&.name} - #{partner&.name}"
  end

  rails_admin do
    visible false
  end
end
