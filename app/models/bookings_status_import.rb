class BookingsStatusImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :imported_count, :uploaded_by, :update_hash

  def initialize(bookings_file = nil, current_user)
    @file = bookings_file
    @uploaded_by = current_user
    @update_hash = {}
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    # when '.xls' then Roo::Spreadsheet.open(file, extension: :xls)
    when '.xlsx' then Roo::Spreadsheet.open(file, extension: :xlsx)
    else raise "Unknown file type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    headers = spreadsheet.row(1) # get header row
    brand_booking_codes = []
    valid_statuses = Booking.brand_booking_statuses.keys
    spreadsheet.each_with_index do |row, idx|
      next if idx == 0
      brand_booking_codes[idx] = row[1]
    end
    if brand_booking_codes.count > 1000
      errors.add :base, "Exceeds limit: Max 1000 records can be updated"
      return
    end
    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header

      # create hash from headers and cells
      booking_data = Hash[[headers, row].transpose]
      if booking_data['BrandBookingCode'].nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: Brand Booking code is blank"
        next
      elsif booking_data['BrandBookingStatus'].nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: Brand Booking status is blank"
        next
      elsif brand_booking_codes.compact.tally[booking_data['BrandBookingCode']] > 1  # checking the count of every brand booking code
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: has duplicate brand code"
        next
      elsif !valid_statuses.include?(booking_data['BrandBookingStatus'].strip.split.map(&:downcase).join("_"))
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: Invalid Brand Booking Status"
        next
      end
      if booking_data['ChargerIssueDate'].present?
        if valid_date?(booking_data['ChargerIssueDate'])
          if booking_data['ChargerIssueDate'].to_date < Date.today
            errors.add :base, "Row #{idx + 1}: Charger date should not be in the past."
          end
        else
          errors.add :base, "Row #{idx + 1}: Charger Issue date format is incorrect."
        end
      end
      if booking_data['CustomerProposedDateSurvey'].present?
        if valid_date?(booking_data['CustomerProposedDateSurvey'])
          if booking_data['CustomerProposedDateSurvey'].to_date < Date.today
            errors.add :base, "Row #{idx + 1}: Customer Proposed Date Survey should not be in the past."
          end
        else
          errors.add :base, "Row #{idx + 1}: Customer Proposed Date Survey format is incorrect."
        end
      end
      if booking_data['DateUpdationProposedSurveyDate'].present?
        if valid_date?(booking_data['DateUpdationProposedSurveyDate'])
          if booking_data['DateUpdationProposedSurveyDate'].to_date < Date.today
            errors.add :base, "Row #{idx + 1}: Date Updation Proposed Survey Date should not be in the past."
          end
        else
          errors.add :base, "Row #{idx + 1}: Date Updation Proposed Survey Date format is incorrect."
        end
      end
      if booking_data['CustomerProposedDateInstallation'].present?
        if valid_date?(booking_data['CustomerProposedDateInstallation'])
          if booking_data['CustomerProposedDateInstallation'].to_date < Date.today
            errors.add :base, "Row #{idx + 1}: Customer Proposed Date Installation should not be in the past."
          end
        else
          errors.add :base, "Row #{idx + 1}: Customer Proposed Date Installation format is incorrect."
        end
      end
      if booking_data['DateUpdationProposedInstallationDate'].present?
        if valid_date?(booking_data['DateUpdationProposedInstallationDate'])
          if booking_data['DateUpdationProposedInstallationDate'].to_date < Date.today
            errors.add :base, "Row #{idx + 1}: Date Updation Proposed Installation Date should not be in the past."
          end
        else
          errors.add :base, "Row #{idx + 1}: Date Updation Proposed Installation Date format is incorrect."
        end
      end

      update_hash[booking_data['BrandBookingCode']] = { 
        brand_booking_status: booking_data['BrandBookingStatus'].strip.split.map(&:downcase).join("_"),
        expected_delivery_date: booking_data['ExpectedDeliveryDate'],
        booking_status_updated_date: booking_data['BookingStatusUpdatedDate'],
        edd_status_updated_date: booking_data['EDDStatusUpdatedDate'],
        charger_issued_date: booking_data['ChargerIssueDate'],
        customer_proposed_survey_date: booking_data['CustomerProposedDateSurvey'],
        customer_proposed_survey_date_updated_at: booking_data['DateUpdationProposedSurveyDate'],
        customer_proposed_install_date: booking_data['CustomerProposedDateInstallation'],
        customer_proposed_install_date_updated_at: booking_data['DateUpdationProposedInstallationDate']
      }
    end

    return if errors.any?

    brand_bookings_codes = update_hash.keys.compact
    bookings = Booking.where(brand_booking_code: brand_bookings_codes, type: "Booking").select(:id, :brand_booking_code)

    missing_brand_booking_codes = brand_bookings_codes - bookings.pluck(:brand_booking_code)

    if missing_brand_booking_codes.count > 0
      errors.add :base, "No bookings for brand booking codes: #{missing_brand_booking_codes.join(", ")}"
      return
    end

    bookings
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    imported_items
    return if errors.any?

    update_array = []
    imported_items.each do |booking|
      update_array << {
                        id: booking.id, brand_booking_status: update_hash[booking.brand_booking_code][:brand_booking_status],
                        expected_delivery_date: update_hash[booking.brand_booking_code][:expected_delivery_date],
                        booking_status_updated_date: update_hash[booking.brand_booking_code][:booking_status_updated_date],
                        edd_status_updated_date: update_hash[booking.brand_booking_code][:edd_status_updated_date],
                        charger_issued_date: update_hash[booking.brand_booking_code][:charger_issued_date],
                        customer_proposed_survey_date: update_hash[booking.brand_booking_code][:customer_proposed_survey_date],
                        customer_proposed_survey_date_updated_at: update_hash[booking.brand_booking_code][:customer_proposed_survey_date_updated_at],
                        customer_proposed_install_date: update_hash[booking.brand_booking_code][:customer_proposed_install_date],
                        customer_proposed_install_date_updated_at: update_hash[booking.brand_booking_code][:customer_proposed_install_date_updated_at]
                      }
    end
    Booking.upsert_all(update_array)
    @imported_count = imported_items.size

    @imported_count
    true
  end

  def valid_date?(value)
    return true if value.is_a?(Date)
    Date.parse(value.to_s)
    true
  rescue ArgumentError
    false
  end
end
