class Complaint < Booking
  belongs_to :survey, class_name: 'Survey', optional: true
  belongs_to :visit_partner, class_name: 'Partner', optional: true

  has_many :customer_connect_logs, as: :loggable, class_name: 'CustomerConnectLog', dependent: :destroy

  has_many :form_submissions, as: :formable, dependent: :destroy
  has_many :visits, class_name: 'Visit', dependent: :destroy

  validates :ecb_complaint_code, allow_blank: true, uniqueness: { case_sensitive: false }, if: -> { type == 'Complaint' }
  validates_presence_of :complaint_receive_date, if: -> { type == 'Complaint' }
  validate :correct_closure, if: :status_closed?

  before_validation :set_ecb_complaint_code, on: :create

  before_update :set_closed_at, if: :status_closed?

  def set_closed_at
    return unless status_changed?
    self.complaint_closed_at = Time.now
  end

  def correct_closure
    return unless visits.count > 0

    visit_statuses = visits.pluck(:status)
    visit_statuses -= ['completed', 'cancelled']
    return unless visit_statuses.count > 0

    errors.add(:status, 'you cannot close the complaint since some visits are still open')
  end


  rails_admin do
    exclude_fields :type, :brand_dealer, :brand_dealer_id, :brand_booking_date,
                   :ecb_receive_date, :ecb_booking_code, :ecb_install_code,
                   :scheduled_install_date_time, :actual_install_date_time, :install_completed_at, :install_partner, :complaint,
                   :layout_approved_at, :layout_approved_by, :charger_issued_date, :extra_cable_customer_pay,
                   :vin, :engine_number, :install_type, :complaint_id, :customer_install_confirmed, :uploaded_by,
                   :customer_connect_logs, :form_submissions, :visits
  end

  def title
    "#{ecb_complaint_code} - #{customer_name} - #{contact_number}"
  end

  private

  def set_ecb_complaint_code
    return unless type == 'Complaint'

    model_code = brand_model ? brand_model.code : '00'
    today = Time.now.in_time_zone('Kolkata').to_date

    prefix = "C-#{brand.code}#{model_code}#{today.strftime('%y%b')}#{city.code}".upcase

    running_count = Complaint.where('ecb_complaint_code LIKE ?',
                                 "#{prefix}%").order(ecb_complaint_code: :desc).first.try(:ecb_complaint_code)

    counter = running_count ? running_count.gsub(prefix, '').to_i : 0
    counter += 1

    self.ecb_complaint_code = "#{prefix}#{counter}"

    loop do
      break unless self.class.exists?(ecb_complaint_code:)

      counter += 1
      self.ecb_complaint_code = "#{prefix}#{counter}"
    end
  end
end
