class Partner < ApplicationRecord
  has_many :user_roles, dependent: :destroy
  has_many :partner_locations, dependent: :destroy
  validates :name, uniqueness: { case_sensitive: false }, presence: true, length: { minimum: 3, maximum: 30 }
  validates :company_name, presence: true, length: { minimum: 3, maximum: 50 }
  validates :code, uniqueness: { case_sensitive: false }, allow_blank: true, length: { minimum: 2, maximum: 5 }

  validates :contact_person, allow_blank: true, length: { minimum: 3, maximum: 30 }
  validates :contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :alt_contact_number, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :billing_address, allow_blank: true, length: { minimum: 3, maximum: 1024 }
  validates :gst, allow_blank: true, length: { minimum: 15, maximum: 15 }

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  scope :active, -> { where(status: :active) }

  default_scope { order(:name) }

  rails_admin do
    exclude_fields :user_roles
  end
end
