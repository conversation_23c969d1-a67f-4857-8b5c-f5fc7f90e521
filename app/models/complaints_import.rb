class ComplaintsImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :imported_count, :uploaded_by

  def initialize(complaints_file = nil, current_user)
    @file = complaints_file
    @uploaded_by = current_user
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    # when '.xls' then Roo::Spreadsheet.open(file, extension: :xls)
    when '.xlsx' then Roo::Spreadsheet.open(file, extension: :xlsx)
    else raise "Unknown file type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    count = spreadsheet.count
    headers = spreadsheet.row(1) # get header row
    complaint_models = []
    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header

      # create hash from headers and cells
      complaint_data = Hash[[headers, row].transpose]

      brand = Brand.find_by(code: complaint_data['BrandCode'], status: :active)
      if brand.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: BrandCode not found"
        next
      end

      if complaint_data['BrandModelCode']
        brand_model = brand.brand_models.find_by(code: complaint_data['BrandModelCode'], status: :active)
        if brand_model.nil?
          errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: BrandModelCode not found"
          next
        end
      end

      if complaint_data['InstallerPartnerCode']
        install_partner = Partner.find_by(code: complaint_data['InstallerPartnerCode'], status: :active)
        if install_partner.nil?
          errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: InstallerPartnerCode not found"
          next
        end
      end

      city_manager_user = User.find_by(mobile: complaint_data['CityManagerMobile'], user_type: :ecb, status: :active)
      if city_manager_user.nil? || !city_manager_user.city_manager?
        errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: CityManagerMobile not found"
        next
      end

      zone = Location.find_by(code: complaint_data['ZoneCode'], location_type: :zone, status: :active)
      if zone.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: ZoneCode not found"
        next
      end

      city = Location.find_by(code: complaint_data['EVCityCode'], location_type: :city, status: :active)
      if city.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: EVCityCode not found"
        next
      end

      state = Location.find_by(code: complaint_data['CustomerStateCode'], location_type: :state, status: :active)
      if state.nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: CustomerStateCode not found"
        next
      end

      survey = Survey.find_by(ecb_survey_code: complaint_data['SurveyNumber'])   
      # if survey.nil?
      #   errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: SurveyNumber not found"
      #   next
      # end
      ecb_survey_code = survey&.ecb_survey_code
      survey_id = survey&.id

      installation = Installation.find_by(ecb_install_code: complaint_data['InstallationNumber'])   
      # if installation.nil?
      #   errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: InstallationNumber not found"
      #   next
      # end
      ecb_install_code = installation&.ecb_install_code

      complaint = Complaint.new(brand:, brand_model:, install_partner:, ecb_survey_code:, survey_id:,
                            ecb_install_code:, assigned_city_manager: city_manager_user, zone:,
                            dealer_name: complaint_data['DealerName'], dealer_location: complaint_data['DealerLocation'],
                            vin: complaint_data['VINNumber'], brand_complaint_number: complaint_data['BrandComplaintNumber'],
                            ecb_survey_code: complaint_data['SurveyNumber'], ecb_install_code: complaint_data['InstallationNumber'],
                            actual_install_date_time: complaint_data['InstallationDate'], complaint_receive_date: complaint_data['ComplaintReceiveDate'],
                            customer_name: complaint_data['CustomerName'], contact_number: complaint_data['CustomerPhone1'],
                            alt_contact_number: complaint_data['CustomerPhone2'], email: complaint_data['CustomerEmail'],
                            address_line1: complaint_data['CustomerAddress1'],
                            address_line2: complaint_data['CustomerAddress2'], city:, state:,
                            pincode: complaint_data['CustomerPincode'], remarks: complaint_data['Remarks'],
                            obc: complaint_data['OBC'], customer_city: complaint_data['CustomerCity'], complaint_brand_model_name: complaint_data['ModelName'],
                            uploaded_by:, model_variant: complaint_data['ModelVariant'], model_colour: complaint_data['ModelColour'],
                            model_description: complaint_data['ModelDescription'])

      unless complaint.valid?
        complaint.errors.full_messages.each do |msg|
          errors.add :base, "Row #{idx + 1}, SerialNo #{complaint_data['SerialNo']}: #{msg}"
        end
        next
      end
      complaint_models << complaint
    end
    complaint_models
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    imported_items
    return if errors.any?

    if imported_items.map(&:valid?).all?
      imported_items.each(&:save!)
      @imported_count = imported_items.count
      true
    else
      false
    end
  end
end
