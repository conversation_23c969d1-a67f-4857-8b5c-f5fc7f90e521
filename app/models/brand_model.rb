class BrandModel < ApplicationRecord
  belongs_to :brand
  validates :name, presence: true, length: { minimum: 3, maximum: 30 }
  validates :code, presence: true, length: { minimum: 2, maximum: 10 }

  validates :name, uniqueness: { case_sensitive: false, scope: %i[brand] }
  validates :code, uniqueness: { case_sensitive: false, scope: %i[brand] }

  has_many :form_section_brands, dependent: :nullify
  has_many :form_sections, through: :form_section_brands

  has_many :form_question_brands, dependent: :nullify
  has_many :form_questions, through: :form_question_brands

  has_many :brand_model_pricings, dependent: :destroy

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  enum charger_size: {
    na: 0,
    kva11: 1,
    kva22: 2,
    "3.3kw": 3,
    "7.2kw": 4,
    "7.4kw": 5,
    "11kw": 6,
    "22kw": 7,
  }, _prefix: true

  scope :active, -> { where(status: :active) }
  scope :payment_trackerable, -> { where(is_required_in_payment_tracker: :true) }

  default_scope { order(:name) }

  rails_admin do
    visible false
    exclude_fields :form_section_brands, :form_questions, :form_question_brands, :form_sections
  end

  def current_pricing(charger_size)
    brand_model_pricings.where(charger_size: charger_size).where("start_date <= :date AND end_date >= :date", date: Date.today).first
  end
end
