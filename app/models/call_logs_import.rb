class CallLogsImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :uploaded_by, :update_booking_array, :create_call_log_array

  def initialize(bookings_file = nil, current_user)
    @file = bookings_file
    @uploaded_by = current_user
    @update_booking_array = []
    @update_call_log_array = []
    @create_call_log_array = []
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    # when '.xls' then Roo::Spreadsheet.open(file, extension: :xls)
    when '.xlsx' then Roo::Spreadsheet.open(file, extension: :xlsx)
    else raise "Unknown file type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    count = spreadsheet.count
    headers = spreadsheet.row(1) # get header row
    ecb_booking_codes = []
    states = []
    state_hash = {}
    booking_hash = {}
    time_now = Time.now

    valid_log_types = [
      "not_picked",
      "switched_off",
      "scheduled",
      "customer_refused",
      "wants_survey_later",
      "any_other_reason",
      "outstation_approval_awaited",
      "cancelled_the_booking",
      "survey_installation_done_against_another_survey_booking_no",
      "survey_installation_done_under_other_dealer",
      "survey_installation_done_against_other_charger_package",
      "customer_did_not_purchase_any_charger_package",
      "call_me_later",
      "cee_customer_ready_for_survey",
      "customer_scope_of_working_pending",
      "customer_wants_installation_later",
      "installation_already_done_by_another_partner",
      "noc_not_provided_by_society",
      "payment_for_extra_cable_is_pending",
      "site_house_under_construction",
      "site_is_good_to_go_for_installation",
      "customer_already_has_an_ev_charger_and_does_not_want_another_charger_installation"
    ]
    

    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header
      # create hash from headers and cells
      call_log_data = Hash[[headers, row].transpose]

      states << call_log_data["State"].strip unless call_log_data["State"].blank?

      ecb_booking_code = call_log_data['BookingRefNumber']
      if !ecb_booking_code.blank? && ecb_booking_codes.include?(ecb_booking_code)
        errors.add :base, "Row #{idx + 1}, SerialNo #{call_log_data['SerialNo']}: Duplicate Bookings Reference Number"
        next
      elsif !ecb_booking_code.blank?
        ecb_booking_codes << ecb_booking_code
      end

      log_type = call_log_data['LogType'].to_s.strip.downcase
      if log_type.blank?
        errors.add :base, "Row #{idx + 1}, SerialNo #{call_log_data['SerialNo']}: Log Type is blank"
        next
      elsif !valid_log_types.include?(log_type)
        errors.add :base, "Row #{idx + 1}, SerialNo #{call_log_data['SerialNo']}: Log Type is invalid"
        next
      end

      mobile_number = call_log_data["CustomerPhoneNumber2"].to_s.strip
      unless mobile_number.blank?
        unless mobile_number.to_i.to_s.length.eql?(10)
          errors.add :base, "Row #{idx + 1}, SerialNo #{call_log_data['SerialNo']}: CustomerPhoneNumber2 is invalid"
          next
        end
      end
    end

    if ecb_booking_codes.count > 1000
      errors.add :base, "Exceeds limit: Max 1000 records can be updated"
      return
    end

    return if errors.any?

    locations = Location.where(name: states, location_type: "state")
    missing_states = states - locations.pluck(:name)

    if missing_states.count > 0
      errors.add :base, "No states found : #{missing_states.join(", ")}"
      return
    end

    locations.each do |state|
      state_hash[state.name] = state.id
    end

    bookings = Booking.where(ecb_booking_code: ecb_booking_codes, type: "Booking")
    missing_ecb_booking_codes = ecb_booking_codes - bookings.pluck(:ecb_booking_code)

    if missing_ecb_booking_codes.count > 0
      errors.add :base, "No bookings for booking reference number: #{missing_ecb_booking_codes.join(", ")}"
      return
    end

    bookings.each do |booking|
      booking_hash[booking.ecb_booking_code] = {
        id: booking.id,
        state_id: booking.state_id,
        alt_contact_number: booking.alt_contact_number,
        address_line1: booking.address_line1,
        pincode: booking.pincode,
        cee_name: booking.cee_name,
        next_follow_up_at: booking.next_follow_up_at,
        cee_contact_date: booking.cee_contact_date,
        first_contact_at: booking.first_contact_at
      }
    end

    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header

      # create hash from headers and cells
      call_log_data = Hash[[headers, row].transpose]

      next_follow_up_at = call_log_data["NextFollowUpDate"].to_s.strip
      ecb_booking_code = call_log_data['BookingRefNumber']
      log_type = call_log_data['LogType'].to_s.strip.downcase

      create_call_log_array << {
        log_type: log_type,
        next_follow_up_at: !next_follow_up_at.blank? ? next_follow_up_at.to_time + 90.minutes : nil,
        summary: call_log_data['Summary'],
        loggable_type: "Booking",
        loggable_id: booking_hash[ecb_booking_code][:id],
        user_id: uploaded_by.id
      }

      state_name = call_log_data["State"].to_s.strip
      cee_name = call_log_data["CEEName"].to_s.strip
      cee_contact_date = call_log_data["CEEContactDate"].to_s.strip
      first_contact_at = call_log_data["FirstContactAt"].to_s.strip
      mobile_number = call_log_data["CustomerPhoneNumber2"].to_s.strip
      customer_address = call_log_data["CustomerAddress"].to_s.strip
      pincode = call_log_data["PinCode"].to_s.strip

      book_hash = booking_hash[ecb_booking_code]
      book_hash[:updated_at] = time_now

      book_hash[:cee_name] = cee_name unless cee_name.blank?
      book_hash[:alt_contact_number] = mobile_number.to_i unless mobile_number.blank?
      book_hash[:state_id] = state_hash[state_name] unless state_name.blank?
      book_hash[:address_line1] = customer_address unless customer_address.blank?
      book_hash[:pincode] = pincode.to_i unless pincode.blank?
      book_hash[:next_follow_up_at] = next_follow_up_at.to_time + 90.minutes unless next_follow_up_at.blank?
      book_hash[:first_contact_at] = first_contact_at.to_time unless first_contact_at.blank?
      book_hash[:cee_contact_date] = cee_contact_date unless cee_contact_date.blank?

      update_booking_array << book_hash
    end

    [create_call_log_array,  update_booking_array]
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    customer_call_logs_data, bookings_data = imported_items
    return if errors.any?

    begin
      ActiveRecord::Base.transaction do
        CustomerConnectLog.create_with(created_at: Time.now).insert_all(customer_call_logs_data)
        Booking.upsert_all(bookings_data)
      end
    rescue => e
      errors.add :base, e.message
      return false
    end

    true
  end
end
