class TransferBookingsImport
  include ActiveModel::Model
  require 'roo'

  attr_accessor :file, :imported_count, :uploaded_by, :update_hash, :mobile_number_hash

  def initialize(transfer_bookings_file = nil, current_user)
    @file = transfer_bookings_file
    @uploaded_by = current_user
    @update_hash = {}
    @mobile_number_hash = {}
  end

  def persisted?
    false
  end

  def open_spreadsheet
    case File.extname(file.original_filename)
    # when '.xls' then Roo::Spreadsheet.open(file, extension: :xls)
    when '.xlsx' then Roo::Spreadsheet.open(file, extension: :xlsx)
    else raise "Unknown file type: #{file.original_filename}"
    end
  end

  def load_imported_items
    spreadsheet = open_spreadsheet
    headers = spreadsheet.row(1) # get header row
    ecb_booking_codes = []
    spreadsheet.each_with_index do |row, idx|
      next if idx == 0
      ecb_booking_codes[idx] = row[1]
    end
    if ecb_booking_codes.count > 1000
      errors.add :base, "Exceeds limit: Max 1000 records can be updated"
      return
    end
    spreadsheet.each_with_index do |row, idx|
      next if idx == 0 # skip header

      # create hash from headers and cells
      booking_data = Hash[[headers, row].transpose]
      if booking_data["NewCityManagerMobileNumber"].nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: mobile number is blank"
        next
      elsif booking_data['BookingRefNumber'].nil?
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: Booking ref number is blank"
        next
      elsif ecb_booking_codes.compact.tally[booking_data['BookingRefNumber']] > 1  # checking the count of every brand booking code
        errors.add :base, "Row #{idx + 1}, SerialNo #{booking_data['SerialNo']}: has duplicate  booking ref number"
        next
      end
      update_hash[booking_data['BookingRefNumber']] = booking_data['NewCityManagerMobileNumber'].to_i.to_s
    end

    return if errors.any?

    mobile_nos = update_hash.values.compact.uniq

    users = User.where(mobile: mobile_nos)
    missing_mobile_nos = mobile_nos - users.pluck(:mobile)

    if missing_mobile_nos.count > 0
      errors.add :base, "No users found for mobile numbers: #{missing_mobile_nos.join(", ")}"
      return
    end

    users.each do |user|
      mobile_number_hash[user.mobile.to_s] = { id: user.id, email: user.email }
    end

    return if errors.any?
    ecb_booking_codes = update_hash.keys.compact
    bookings = Booking.where(ecb_booking_code: ecb_booking_codes)
    missing_ecb_booking_codes = ecb_booking_codes - bookings.pluck(:ecb_booking_code)

    if missing_ecb_booking_codes.count > 0
      errors.add :base, "No bookings for booking reference number: #{missing_ecb_booking_codes.join(", ")}"
      return
    end

    bookings
  end

  def imported_items
    @imported_items ||= load_imported_items
  end

  def save
    imported_items
    return if errors.any?

    update_array = []
    imported_items.each do |booking|
      mobile_number_key_value = mobile_number_hash[update_hash[booking.ecb_booking_code]] || {}
      update_array << { id: booking.id, assigned_city_manager_id: mobile_number_key_value[:id] ,city_manager_email: mobile_number_key_value[:email] }
    end
    Booking.upsert_all(update_array)
    @imported_count = imported_items.size

    @imported_count
    true
  end
end
