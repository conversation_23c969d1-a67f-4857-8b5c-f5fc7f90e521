class Location < ApplicationRecord
  has_many :child_locations, class_name: 'Location', dependent: :nullify, foreign_key: 'parent'
  belongs_to :parent, class_name: 'Location', optional: true
  has_many :user_roles, dependent: :nullify
  has_many :partner_locations, dependent: :destroy

  validates :name, uniqueness: { case_sensitive: false, scope: %i[location_type] }, presence: true,
                   length: { minimum: 3, maximum: 30 }
  validates :code, uniqueness: { case_sensitive: false, scope: %i[location_type] }, presence: true,
                   length: { minimum: 2, maximum: 10 }

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  enum location_type: {
    city: 0,
    district: 1,
    state: 2,
    zone: 3
  }, _prefix: true

  scope :active, -> { where(status: :active) }
  scope :test_zones, lambda {
                       where('location_type = :location_type AND lower(name) LIKE :name', location_type: location_types[:zone], name: '%test%')
                     }
  scope :not_test_location, -> { where('lower(name) NOT LIKE :name', name: 'test%') }

  default_scope { order(:name) }

  rails_admin do
    exclude_fields :partner_locations, :user_roles
  end
end
