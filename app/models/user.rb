class User < ApplicationRecord
  include DeviseTokenAuth::Concerns::User
  devise :database_authenticatable, :trackable, :uid
  devise authentication_keys: %i[mobile email]

  has_many :user_roles, dependent: :destroy
  has_many :user_fcm_registrations, dependent: :destroy
  belongs_to :brand, optional: true
  validates :brand_id, uniqueness: true, allow_blank: true
  # has_many :assignments, dependent: :destroy

  validates :name, presence: true, length: { minimum: 3, maximum: 30 }

  validates :email, uniqueness: { case_sensitive: false }, presence: true,
                    length: { minimum: 4, maximum: 100 }, format: { with: URI::MailTo::EMAIL_REGEXP }

  validates :mobile, presence: true, numericality: true, uniqueness: true, length: { minimum: 10, maximum: 10 }
  validates :alternate_mobile, allow_blank: true, numericality: true, length: { minimum: 10, maximum: 10 }
  validates :aadhaar, allow_blank: true, numericality: true, length: { minimum: 12, maximum: 12 }
  validates :employee_id, allow_blank: true, length: { minimum: 2, maximum: 20 }

  before_update :clear_brand, unless: :user_type_client?

  enum status: {
    active: 0,
    inactive: 1,
    disabled: 2
  }, _prefix: true

  enum user_type: {
    ecb: 0,
    external: 1,
    client: 2
  }, _prefix: true

  attr_writer :login

  scope :active, -> { where(status: :active) }

  default_scope { order(:name) }

  # after_save :save_sendbird_user

  def build_auth_headers(token, client = 'default')
    Rails.logger.info "[AUTH DEBUG] build_auth_header called with token: #{token.present? ? 'present' : 'nil'}, client: #{client}"

    # client may use expiry to prevent validation request if expired
    # must be cast as string or headers will break
    expiry = tokens[client]['expiry'] || tokens[client][:expiry]

    Rails.logger.info "[AUTH DEBUG] Token data for client '#{client}': #{tokens[client]}"
    Rails.logger.info "[AUTH DEBUG] Expiry: #{expiry}"

    headers = {
      DeviseTokenAuth.headers_names[:"access-token"] => token,
      DeviseTokenAuth.headers_names[:"client"] => client,
      DeviseTokenAuth.headers_names[:"expiry"] => expiry.to_s,
      DeviseTokenAuth.headers_names[:"uid"] => uid,
      DeviseTokenAuth.headers_names[:"token-type"] => "Bearer"
    }

    Rails.logger.info "[AUTH DEBUG] Built headers: #{headers}"
    headers.merge(build_bearer_token(headers))
  end

  def self.city_managers(city_id)
    User.joins(:user_roles).where(user_roles: { role_type: :city_manager, location_id: city_id })
  end

  def self.zonal_managers(zone_id)
    User.joins(:user_roles).where(user_roles: { role_type: :zonal_manager, location_id: zone_id })
  end

  def self.mis_users
    User.joins(:user_roles).where(user_roles: { role_type: :mis })
  end

  def self.management_users
    User.joins(:user_roles).where(user_roles: { role_type: :management })
  end

  def self.super_users
    User.joins(:user_roles).where(user_roles: { role_type: :super_user })
  end

  def self.tech_users
    User.joins(:user_roles).where(user_roles: { role_type: :central_tech })
  end

  def login
    @login || mobile || email
  end

  def power_user?
    user_roles.where(role_type: %i[management super_user]).any?
  end

  def city_manager?
    user_roles.where(role_type: :city_manager).any?
  end

  def zonal_manager?
    user_roles.where(role_type: :zonal_manager).any?
  end

  def finance_controller?
    user_roles.where(role_type: :finance_controller).any?
  end

  def get_zones_for_zonal_manager
    user_roles.where(role_type: :zonal_manager).map(&:location)
  end

  def field_agent?
    user_roles.where(role_type: :field_agent).any?
  end

  def partner_id
    user_roles.where(role_type: %i[partner_manager field_agent]).first&.partner&.id
  end

  def partner_manager?
    user_roles.where(role_type: :partner_manager).any?
  end

  def auth_user?
    user_roles.where(role_type: %i[field_agent partner_manager city_manager zonal_manager finance_controller mis management super_user
                                   central_tech]).any? || client?
  end

  def editor_user?
    user_roles.where(role_type: %i[city_manager zonal_manager finance_controller mis management super_user central_tech]).any?
  end

  def import_user?
    user_roles.where(role_type: %i[zonal_manager mis management super_user central_tech]).any?
  end

  def finance_user?
    user_roles.where(role_type: %i[mis finance_controller super_user]).any?
  end

  def mis_user?
    user_roles.where(role_type: %i[mis management super_user central_tech]).any?
  end

  def super_user?
    user_roles.where(role_type: %i[super_user]).any?
  end

  def client?
    user_type.eql?('client')
  end

  def self.find_for_database_authentication(warden_conditions)
    conditions = warden_conditions.dup
    if (login = conditions.delete(:login))
      where(conditions.to_h).where(['mobile = :value OR lower(email) = :value',
                                    { value: login.downcase }]).first
    elsif conditions.key?(:mobile) || conditions.key?(:email)
      where(conditions.to_h).first
    end
  end

  def active_for_authentication?
    super && status_active?
  end

  def confirmation_required?
    false
  end

  rails_admin do
    exclude_fields :reset_password_sent_at, :reset_password_token, :remember_created_at, :sign_in_count,
                   :current_sign_in_at, :current_sign_in_ip, :last_sign_in_at, :last_sign_in_ip, :tokens, :uid,
                   :provider, :allow_password_change, :nickname, :image
  end

  # def sendbird_payload
  #   {
  #     user_id: mobile,
  #     nickname: name,
  #     phone_number: "+91#{mobile}",
  #     profile_url: '',
  #     issue_access_token: true,
  #     metadata: {
  #       email:,
  #       alternate_mobile:,
  #       user_type:,
  #       uid:,
  #       id:
  #     }
  #   }
  # end

  # def save_sendbird_user
  #   return if sendbird_token.present? && !saved_change_to_attribute(:mobile)

  #   token = Sendbird::Client.new.create_user(sendbird_payload)
  #   update_column(:sendbird_token, token) if token.present?
  # end
  private

  def clear_brand
    self.brand_id = nil
  end
end
