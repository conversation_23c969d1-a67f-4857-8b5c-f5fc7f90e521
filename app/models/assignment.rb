class Assignment < ApplicationRecord
  belongs_to :assignable, polymorphic: true
  belongs_to :user

  enum status: {
    assigned: 0,
    removed: 1
  }, _prefix: true

  before_create :set_assigned_at
  before_update :set_assigned_at, if: :status_changed?

  scope :booking_type, -> { where(assignable_type: 'Booking') }
  scope :active, -> { where(status: :assigned) }

  validate :correct_role_type

  enum role_type: {
    field_agent: 1,
    partner_manager: 2,
    city_manager: 3,
    central_tech: 8
  }, _prefix: true

  def title
    "#{assignable.title} - #{user.name}"
  end

  def set_assigned_at
    self.assigned_at = Time.now if status_assigned?
    self.removed_at = Time.now if status_removed?
  end

  def correct_role_type
    return unless user.user_roles.where(role_type:).empty?

    errors.add(:role_type, '- Incorrect role for user')
  end

  rails_admin do
    visible false
    exclude_fields :assigned_at, :removed_at
  end
end
