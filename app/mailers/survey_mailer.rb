class SurveyMailer < ApplicationMailer
  def survey_scheduled(survey)
    @survey = survey
    mail(
      to: @survey.email,
      subject: 'Scheduled Date and Time for the Survey'
    )
  end

  def survey_cancelled(survey)
    @survey = survey
    mail(
      to: @survey.email,
      subject: 'Cancellation of the Survey'
    )
  end

  def charger_delivered(survey)
    @survey = survey
    mail(
      to: @survey.email,
      subject: 'Information about Charger Delivery'
    )
  end

  def seek_payment_email(survey, from_email, recipient_emails, subject, body, cc_emails, bcc_emails, attachment_files)
    @survey = survey
    @payment_link = survey.payment_link
    Rails.logger.info("Payment link: #{@payment_link}")
    @body = body.gsub('{{PAYMENT_LINK}}', @payment_link)
    @subject = subject || "Feasibility Study for #{@survey.brand&.name} EV : #{@survey.customer_name}"
    attachment_files&.each do |file|
      attachments[file.original_filename] = file.read
    end

    mail(
      from: from_email || Rails.application.credentials[Rails.env.to_sym].dig(:sendgrid, :from_email),
      to: recipient_emails.empty? ? @survey.email : recipient_emails,
      cc: cc_emails,
      bcc: bcc_emails,
      subject: @subject
    )
  end

  def refund_email(survey, from_email, recipient_emails, subject, body, cc_emails, bcc_emails, attachment_files)
    @survey = survey
    @body = body
    @subject = subject || "Feasibility Study for #{@survey.brand&.name.to_s} EV : #{@survey.customer_name.to_s}"
    attachment_files&.each do |file|
      attachments[file.original_filename] = file.read
    end

    mail(
      from: from_email || Rails.application.credentials[Rails.env.to_sym].dig(:sendgrid, :from_email),
      to: recipient_emails.empty? ? @survey.email : recipient_emails,
      cc: cc_emails,
      bcc: bcc_emails,
      subject: @subject
    )
  end

  def refund_processed_email(survey, from_email, recipient_emails, subject, body, cc_emails, bcc_emails, attachment_files)
    @survey = survey
    @body = body
    @subject = subject || "Feasibility Study for #{@survey.brand&.name.to_s} EV : #{@survey.customer_name.to_s}"

    attachment_files&.each do |file|
      attachments[file.original_filename] = file.read
    end

    mail(
      from: from_email || Rails.application.credentials[Rails.env.to_sym].dig(:sendgrid, :from_email),
      to: recipient_emails.empty? ? @survey.email : recipient_emails,
      cc: cc_emails,
      bcc: bcc_emails,
      subject: @subject
    )
  end
end
