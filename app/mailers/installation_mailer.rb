class InstallationMailer < ApplicationMailer
  def installation_scheduled(installation)
    @installation = installation
    @is_charger_delivered = @installation.charger_issued_date.present? && @installation.is_sow_completed
    mail(
      to: @installation.email,
      subject: @is_charger_delivered ? 'Information about Charger Installation' : 'Scheduled Date and Time for the Installation'
    )
  end

  def installation_cancelled(installation)
    @installation = installation
    mail(
      to: @installation.email,
      subject: 'Cancellation of the Installation'
    )
  end
end
