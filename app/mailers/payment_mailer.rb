class PaymentMailer < ApplicationMailer
  def payment_action(assignment, action_type, user)
    @assignment = assignment
    @action = action_type
    @user = user
    cc_emails = []
    cc_emails << '<EMAIL>'
    cc_emails << '<EMAIL>'
    cc_emails << '<EMAIL>'
    mail(
      to: '<EMAIL>',
      cc: cc_emails.compact.uniq.join(','),
      subject: "Payment #{action_type} from #{assignment.customer_name} for #{assignment.code}"
    )
  end
end
