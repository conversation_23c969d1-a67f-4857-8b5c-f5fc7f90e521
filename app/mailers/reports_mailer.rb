class ReportsMailer < ApplicationMailer
  def report_generate_success(recipient, report_name, start_date, end_date, attachment_file)
    @recipient = recipient
    @start_date = start_date
    @end_date = end_date
    @report_name = report_name
    attachments["#{@report_name}.xlsx"] = attachment_file.read
    mail(
      to: recipient.email,
      subject: "#{@report_name} report is generated successfully"
    )
  end

  def report_generate_failed(recipient, report_name, start_date, end_date)
    @recipient = recipient
    @start_date = start_date
    @end_date = end_date
    @report_name = report_name
    mail(
      to: recipient.email,
      subject: "#{@report_name} report generation failed"
    )
  end
end
