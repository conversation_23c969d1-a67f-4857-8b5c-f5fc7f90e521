const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

class SetuUPIService {
  constructor() {
    // Configuration - these should be stored in environment variables or AWS Secrets Manager
    this.clientId = process.env.SETU_CLIENT_ID;
    this.clientSecret = process.env.SETU_CLIENT_SECRET;
    this.merchantId = process.env.SETU_MERCHANT_ID;
    this.merchantVpa = process.env.SETU_MERCHANT_VPA;
    this.qrUrl = process.env.SETU_SQR_URL;

    // API URLs
    this.loginUrl = 'https://accountservice.setu.co/v1/users/login';

    this.accessToken = null;
  }

  async getAccessToken() {
    try {
      const headers = {
        'client': 'bridge',
        'Content-Type': 'application/json'
      };

      const payload = {
        clientID: this.clientId,
        secret: this.clientSecret,
        grant_type: 'client_credentials'
      };

      console.log('Requesting access token from Setu');
      const response = await axios.post(this.loginUrl, payload, {
        headers,
        timeout: 30000
      });

      if (response.status === 200 && response.data.access_token) {
        this.accessToken = response.data.access_token;
        console.log('Successfully obtained access token');
        return this.accessToken;
      } else {
        console.error(`Failed to get access token. Status: ${response.status}`);
        throw new Error(`Authentication failed: ${response.status}`);
      }

    } catch (error) {
      console.error('Network error during authentication:', error.message);
      throw new Error(`Network error during authentication: ${error.message}`);
    }
  }

  async generateQr(amount, surveyCode, transactionNote) {
    try {
      // Get access token if not already available
      if (!this.accessToken) {
        await this.getAccessToken();
      }

      // Generate expiry date (24 hours from now)
      const expiryDate = new Date(Date.now() + 24 * 60 * 60 * 1000)
        .toISOString()
        .replace('Z', '+05:30');

      // Generate unique reference ID
      const referenceId = uuidv4().replace(/-/g, '').substring(0, 16);

      const headers = {
        'Authorization': `Bearer ${this.accessToken}`,
        'content-type': 'application/json',
        'merchantId': this.merchantId
      };

      const paymentAmount = Math.round(amount * 100);
      const payload = {
        amount: parseFloat(paymentAmount),
        expiryDate: expiryDate,
        merchantVpa: this.merchantVpa,
        metadata: {
          SurveyCode: surveyCode,
          InvoiceNumber: `INV-${new Date().toISOString().slice(2, 7).replace('-', '-')}-${referenceId.substring(0, 6)}`
        },
        minAmount: parseFloat(paymentAmount),
        referenceId: referenceId,
        transactionNote: transactionNote
      };

      console.log(`Generating QR code for amount: ${amount}, survey_code: ${surveyCode}`);
      let response = await axios.post(this.qrUrl, payload, {
        headers,
        timeout: 30000
      });

      if (response.status === 200) {
        console.log('Successfully generated QR code');
        return response.data;
      } else {
        console.error(`Failed to generate QR code. Status: ${response.status}, Response: ${JSON.stringify(response.data)}`);

        // Try refreshing token if unauthorized
        if (response.status === 401) {
          console.log('Token might be expired, refreshing...');
          this.accessToken = null;
          await this.getAccessToken();

          // Retry with new token
          headers['Authorization'] = `Bearer ${this.accessToken}`;
          response = await axios.post(this.qrUrl, payload, {
            headers,
            timeout: 30000
          });

          if (response.status === 200) {
            console.log('Successfully generated QR code after token refresh');
            return response.data;
          }
        }

        throw new Error(`QR generation failed: ${response.status} - ${JSON.stringify(response.data)}`);
      }

    } catch (error) {
      if (error.response && error.response.status === 401) {
        // Handle token expiration
        console.log('Token expired, refreshing...');
        this.accessToken = null;

        try {
          await this.getAccessToken();

          // Retry the request
          const headers = {
            'Authorization': `Bearer ${this.accessToken}`,
            'content-type': 'application/json',
            'merchantId': this.merchantId
          };

          const expiryDate = new Date(Date.now() + 24 * 60 * 60 * 1000)
            .toISOString()
            .replace('Z', '+05:30');

          const referenceId = uuidv4().replace(/-/g, '').substring(0, 16);

          const payload = {
            amount: parseFloat(amount),
            expiryDate: expiryDate,
            merchantVpa: this.merchantVpa,
            metadata: {
              SurveyCode: surveyCode,
              InvoiceNumber: `INV-${new Date().toISOString().slice(2, 7).replace('-', '-')}-${referenceId.substring(0, 6)}`
            },
            minAmount: parseFloat(amount),
            referenceId: referenceId,
            transactionNote: transactionNote
          };

          const retryResponse = await axios.post(this.qrUrl, payload, {
            headers,
            timeout: 30000
          });

          if (retryResponse.status === 200) {
            console.log('Successfully generated QR code after token refresh');
            return retryResponse.data;
          }
        } catch (retryError) {
          console.error('Failed to generate QR code after token refresh:', retryError.message);
          throw new Error(`QR generation failed after retry: ${retryError.message}`);
        }
      }

      console.error('Network error during QR generation:', error.message);
      throw new Error(`Network error during QR generation: ${error.message}`);
    }
  }
}

exports.handler = async (event, context) => {
  try {
    // Parse the request body
    let body;
    if (typeof event.body === 'string') {
      body = JSON.parse(event.body);
    } else {
      body = event.body || {};
    }

    // Extract parameters
    const amount = body.amount;
    const surveyCode = body.survey_code || '';
    const transactionNote = body.transaction_note || '';

    // Validate required parameters
    if (!amount) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          error: 'Missing required parameter: amount'
        })
      };
    }

    // Initialize Setu service
    const setuService = new SetuUPIService();

    // Generate QR code
    const qrResponse = await setuService.generateQr(amount, surveyCode, transactionNote);

    // Return successful response
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(qrResponse)
    };

  } catch (error) {
    console.error('Lambda execution error:', error.message);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};

// For local testing
if (require.main === module) {
  // Test event
  const testEvent = {
    body: JSON.stringify({
      amount: 100,
      survey_code: 'SURVEY123',
      transaction_note: 'Test payment'
    })
  };

  exports.handler(testEvent, {})
    .then(result => {
      console.log('Test Result:', JSON.stringify(result, null, 2));
    })
    .catch(error => {
      console.error('Test Error:', error);
    });
}
