const axios = require('axios');

// Environment variables for configuration
const CLIENT_ID = process.env.SETU_CLIENT_ID;
const CLIENT_SECRET = process.env.SETU_CLIENT_SECRET;
const MERCHANT_ID = process.env.SETU_MERCHANT_ID;
const MERCHANT_VPA = process.env.SETU_MERCHANT_VPA;
const LOGIN_URL = 'https://accountservice.setu.co/v1/users/login';
const QR_URL = process.env.SETU_SQR_URL || 'https://umap-uat-core.setu.co/api/v1/merchants/dqr';

/**
 * Get access token from Setu API
 * @returns {Promise<string>} Access token
 */
async function getAccessToken() {
  try {
    const response = await axios.post(LOGIN_URL, {
      clientID: CLIENT_ID,
      secret: CLIENT_SECRET,
      grant_type: 'client_credentials'
    }, {
      headers: {
        'client': 'bridge',
        'Content-Type': 'application/json'
      }
    });

    console.log('Successfully obtained access token');
    return response.data.access_token;
  } catch (error) {
    console.error('Error getting access token:', error.response?.data || error.message);
    throw new Error('Failed to get access token from Setu');
  }
}

/**
 * Generate QR code for UPI payment
 * @param {string} accessToken - Setu access token
 * @param {number} amount - Payment amount
 * @param {string} surveyCode - Survey code
 * @param {string} transactionNote - Transaction note
 * @returns {Promise<Object>} QR code data
 */
async function generateQrCode(accessToken, amount, surveyCode, transactionNote) {
  try {
    // Set expiry date to 30 days from now
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30);
    const paymentAmount = Math.round(amount * 100);

    const response = await axios.post(QR_URL, {
      amount: paymentAmount,
      expiryDate: expiryDate.toISOString(),
      merchantVpa: MERCHANT_VPA,
      metadata: {
        surveyCode: surveyCode
      },
      minAmount: paymentAmount,
      referenceId: surveyCode.replace('-', ''),
      transactionNote: transactionNote
    }, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'content-type': 'application/json',
        'merchantId': MERCHANT_ID
      }
    });

    console.log(`Successfully generated QR code for survey: ${surveyCode}`);
    return response.data;
  } catch (error) {
    console.error('Error generating QR code:', error.response?.data || error.message);
    throw new Error('Failed to generate QR code from Setu: ' + (error.response?.data.detail || error.message));
  }
}

/**
 * Lambda handler function
 * @param {Object} event - Lambda event
 * @returns {Promise<Object>} Response
 */
exports.handler = async (event) => {
  try {
    console.log('Received event:', JSON.stringify(event));

    // Parse input parameters - handle both direct invocation and API Gateway
    let params;

    if (event.body) {
      // API Gateway invocation - body is a string that needs to be parsed
      try {
        params = JSON.parse(event.body);
      } catch (error) {
        console.error('Error parsing event body:', error);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({ error: 'Invalid JSON in request body' })
        };
      }
    } else {
      // Direct Lambda invocation
      params = event;
    }

    const { amount, surveyCode, transactionNote } = params;

    // Validate input parameters
    if (!amount || !surveyCode) {
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({ error: 'Missing required parameters: amount and surveyCode are required' })
      };
    }

    // Get access token
    const accessToken = await getAccessToken();

    // Generate QR code
    const qrData = await generateQrCode(
      accessToken,
      amount,
      surveyCode,
      transactionNote || `Payment for ${surveyCode}`
    );

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(qrData)
    };
  } catch (error) {
    console.error('Error in lambda function:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({ error: error.message })
    };
  }
};
