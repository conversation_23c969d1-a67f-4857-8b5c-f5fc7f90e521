AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: Setu QR Generator Lambda Function

Parameters:
  SetuClientId:
    Type: String
    Description: Setu Client ID
    NoEcho: true

  SetuClientSecret:
    Type: String
    Description: Setu Client Secret
    NoEcho: true

  SetuMerchantId:
    Type: String
    Description: Setu Merchant ID
    NoEcho: true

  SetuMerchantVpa:
    Type: String
    Description: Setu Merchant VPA
    NoEcho: true

  SetuSqrUrl:
    Type: String
    Description: Setu Merchant VPA
    NoEcho: true

Resources:
  SetuQrGeneratorFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: index.handler
      Runtime: nodejs18.x
      Architectures:
        - x86_64
      MemorySize: 128
      Timeout: 10
      Environment:
        Variables:
          SETU_CLIENT_ID: !Ref SetuClientId
          SETU_CLIENT_SECRET: !Ref SetuClientSecret
          SETU_MERCHANT_ID: !Ref SetuMerchantId
          SETU_MERCHANT_VPA: !Ref SetuMerchantVpa
          SETU_SQR_URL: !Ref SetuSqrUrl
      Events:
        ApiEvent:
          Type: Api
          Properties:
            Path: /generate-qr
            Method: post

Outputs:
  SetuQrGeneratorFunction:
    Description: "Setu QR Generator Lambda Function ARN"
    Value: !GetAtt SetuQrGeneratorFunction.Arn

  SetuQrGeneratorApi:
    Description: "API Gateway endpoint URL for Prod stage for Setu QR Generator function"
    Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/generate-qr/"
