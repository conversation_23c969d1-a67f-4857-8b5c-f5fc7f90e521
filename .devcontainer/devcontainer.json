// For format details, see https://aka.ms/vscode-remote/devcontainer.json or this file's README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.112.0/containers/docker-existing-docker-compose
// If you want to run as a non-root user in the container, see .devcontainer/docker-compose.yml.
{
  "name": "ECB App Runner",
  // Update the 'dockerComposeFile' list if you have more compose files or use different names.
  // The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
  "dockerComposeFile": [
    "../docker-compose.yml"
  ],
  // The 'service' property is the name of the service for the container that VS Code should
  // use. Update this value and .devcontainer/docker-compose.yml to the real service name.
  "service": "runner",
  // The optional 'workspaceFolder' property is the path VS Code should open by default when
  // connected. This is typically a file mount in .devcontainer/docker-compose.yml
  "workspaceFolder": "/usr/src/app",
  // Set *default* container specific settings.json values on container create.
  "settings": {
    "terminal.integrated.shell.linux": "/bin/bash",
    "editor.formatOnSave": true,
    "editor.formatOnSaveTimeout": 5000,
    "ruby.format": "rubocop",
    // use stylelint to format scss prettier rules
    "editor.codeActionsOnSave": {
      "source.fixAll.stylelint": true
    },
    // prevent VS Code from validating CSS with the editor's built-in linters
    "css.validate": false,
    "less.validate": false,
    "scss.validate": false,
    // Validate with Stylint extension only
    "stylelint.syntax": "scss",
    // Explicit list of languages to validate with stylelint
    // Prevents stylelint from validating default set of languages e.g. JS, MD, SVG
    "stylelint.validate": [
      "scss",
      "css",
      "sass"
    ],
    "stylelint.snippet": [
      "scss"
    ],
    "markdownlint.config": {
      "default": true,
      "no-duplicate-heading": {
        "siblings_only": true
      },
      "no-hard-tabs": false
    },
    "remote.containers.copyGitConfig": true
  },
  // Add the IDs of extensions you want installed when the container is created.
  "extensions": [
    "eamodio.gitlens",
    "EditorConfig.EditorConfig",
    "misogi.ruby-rubocop",
    "redhat.vscode-yaml",
    "editorconfig.editorconfig",
    "github.vscode-pull-request-github",
    "Augment.vscode-augment",
    "GitHub.copilot",
    "GitHub.copilot-chat"
  ],
  "git.enableCommitSigning": false,
  // Uncomment the next line if you want start specific services in your Docker Compose config.
  "runServices": [
    "runner"
  ],
  "[ruby]": {
    "editor.defaultFormatter": "misogi.ruby-rubocop"
  },
  "mounts": [
    "source=${localEnv:HOME}/.ssh,target=/home/<USER>/.ssh,type=bind,consistency=cached",
    "source=${localEnv:HOME}/.gnupg,target=/home/<USER>/.gnupg,type=bind,consistency=cached"
  ]
  // Uncomment the next line if you want to keep your containers running after VS Code shuts down.
  // "shutdownAction": "none",
  // Uncomment the next line to run commands after the container is created - for example installing git.
  // "postCreateCommand": "apt-get update && apt-get install -y git",
  // Uncomment to connect as a non-root user. See https://aka.ms/vscode-remote/containers/non-root.
  // "remoteUser": "vscode"
}
