module Sendbird
  class Client
    def initialize
      @connection = Faraday.new(
        url: "https://api-#{Rails.application.credentials[Rails.env.to_sym].dig(:send_bird,
                                                                                :application_id)}.sendbird.com/v3/",
        headers: { 'Api-Token' => Rails.application.credentials[Rails.env.to_sym].dig(:send_bird, :api_token) }
      )
    end

    def users
      @connection.get('users')
    end

    def add_users_to_channel(channel_url, user_ids)
      response = @connection.post("group_channels/#{channel_url}/invite") do |req|
        req.body = { user_ids: }.to_json
      end
      if response.success?
        Rails.logger.info("Sendbird users #{user_ids} added in channel: #{channel_url} : #{response.body}")
      else
        Rails.logger.error("Sendbird users #{user_ids} addition failed for channel: #{channel_url} : #{response.body}")
      end
    end

    def remove_users_from_channel(channel_url, user_ids)
      response = @connection.put("group_channels/#{channel_url}/leave") do |req|
        req.body = { user_ids: }.to_json
      end
      if response.success?
        Rails.logger.info("Sendbird users #{user_ids} removed from channel: #{channel_url} : #{response.body}")
      else
        Rails.logger.error("Sendbird users #{user_ids} removal failed for channel: #{channel_url} : #{response.body}")
      end
    end

    def create_channel(payload)
      Rails.logger.info("Sendbird data: #{payload}")
      response = if payload[:sendbird_channel].present?
                   @connection.put("group_channels/#{payload[:sendbird_channel]}") do |req|
                     req.body = payload.to_json
                   end
                 else
                   @connection.post('group_channels') do |req|
                     req.body = payload.to_json
                   end
                 end
      if response.success?
        channel_url = JSON.parse(response.body, symbolize_names: true)[:channel_url]
        if channel_url.present?
          response = @connection.post("group_channels/#{channel_url}/metadata") do |req|
            req.body = payload.to_json
          end
          Rails.logger.info("Sendbird metadata pushed: #{response.body}")
        end
        channel_url
      else
        Rails.logger.error("Sendbird channel creation failed with error: #{response.body}")
        nil
      end
    end

    def create_user(payload)
      response = @connection.post('users') do |req|
        Rails.logger.info("Sendbird data: #{payload}")
        req.body = payload.to_json
      end
      if !response.success? && JSON.parse(response.body, symbolize_names: true)[:code] == 400_202
        response = @connection.put("users/#{payload[:user_id]}") do |req|
          Rails.logger.info("Sendbird data: #{payload.except(:user_id)}")
          req.body = payload.except(:user_id).to_json
        end
      end
      if response.success?
        JSON.parse(response.body, symbolize_names: true)[:access_token]
      else
        Rails.logger.error("Sendbird user creation failed for user: #{payload[:user_id]} with error: #{response.body}")
        nil
      end
    end
  end
end
