class Report
  REPORT_TYPES = {
    "database" => "Database 1",
    "database_two" => "Database 2",
    "database_three" => "Database 3",
    "database_recent_new" => "Database - Recently Updated (new)",
    "installations" => "IR Master",
    "invoicing_installation" => "Invoicing Reports Installations",
    "invoicing_survey" => "Invoicing Reports Surveys",
    "payment_tracker" => "Payment Tracker",
    "payment_tracker_recent" => "Payment Tracker Recently Updated",
    "complaints_database" => "Complaints Database",
    "client_database" => "Client Database"
  }
  MAX_RECORDS_PER_PAGE = 1000

  attr_accessor :user, :start_date, :end_date, :brand_id, :brand_model_id , :zone_id, :page_number

  def initialize(user, filters, page_number=nil)
    @user = user
    @start_date = filters[:start_date]
    @end_date = filters[:end_date]
    @brand_id = filters[:brand_id]
    @brand_model_id = filters[:brand_model_id]
    @zone_id = filters[:zone_id]
    @page_number = page_number
  end

  def database_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Booking', created_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def database_two_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Booking', created_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def database_three_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Booking', created_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def database_recent_new_assignments
    installations = Installation.where(updated_at: date_range)
    surveys = Survey.where(updated_at: date_range).where.not(id: installations.pluck(:survey_id))
    bookings = Booking.where(updated_at: date_range, type: "Booking").where.not(id: [surveys.pluck(:booking_id), installations.pluck(:booking_id)].flatten.compact)
    booking_ids = installations.pluck(:booking_id) + surveys.pluck(:booking_id) + bookings.pluck(:id)
    assignments = Booking.where(type: 'Booking', id: booking_ids)
    assignments = apply_filters(assignments)
    assignments
  end

  def client_database_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Booking', created_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def installations_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Installation', status: :completed, approved_at: date_range)
    assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def invoicing_installation_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Installation', status: :completed, approved_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def invoicing_survey_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Survey', status: :completed, approved_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def new_payment_tracker_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Survey', status: :completed, approved_at: date_range).joins(:brand_model).where(brand_models: { is_required_in_payment_tracker: true }).reorder(approved_at: :desc)
    assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def payment_tracker_recent_assignments
    assignments = load_assignments
    assignments = assignments.where(type: 'Survey', status: :completed, updated_at: date_range).joins(:brand_model).where(brand_models: { is_required_in_payment_tracker: true }).reorder(approved_at: :desc)
    assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  def complaints_database_assignments
    assignments = Complaint.where(created_at: date_range)
    assignments = assignments.offset((page_number.to_i - 1)*MAX_RECORDS_PER_PAGE).limit(MAX_RECORDS_PER_PAGE) if page_number.present?
    assignments
  end

  # Deprecated
  def database_recent_assignments
    installations = Installation.where(updated_at: date_range).includes(
      :customer_connect_logs,
      :assigned_city_manager,
      :city,
      :state,
      :zone,
      :uploaded_by,
      survey: [
        :customer_connect_logs,
        :install_partner,
        :assigned_city_manager,
        :city,
        :state,
        :zone,
        :uploaded_by
      ],
      booking: [
        :customer_connect_logs,
        :install_partner,
        :assigned_city_manager,
        :city,
        :state,
        :zone,
        :uploaded_by
      ]
    )
    installations = apply_filters(installations)
    surveys = Survey.where(updated_at: date_range).where.not(id: installations.pluck(:survey_id)).includes(
      :customer_connect_logs,
      :assigned_city_manager,
      :city,
      :state,
      :zone,
      :uploaded_by,
      booking: [
        :customer_connect_logs,
        :install_partner,
        :assigned_city_manager,
        :city,
        :state,
        :zone,
        :uploaded_by
      ]
    )
    surveys = apply_filters(surveys)
    bookings = Booking.where(updated_at: date_range, type: "Booking").where.not(id: [surveys.pluck(:booking_id), installations.pluck(:booking_id)].flatten.compact).includes(
      :customer_connect_logs,
      :assigned_city_manager,
      :city,
      :state,
      :zone,
      :uploaded_by
    )
    bookings = apply_filters(bookings)
    [bookings, surveys, installations]
  end

  private

  def load_assignments
    assignments = if user.eql?('sidekiq') || user.mis_user?
                    Booking.all
                  elsif user.zonal_manager?
                    Booking.where(zone: user.get_zones_for_zonal_manager)
                  elsif user.city_manager?
                    Booking.where(assigned_city_manager_id: user.id)
                  elsif user.client?
                    Booking.where(brand_id: user.brand_id).where.not(zone_id: Location.test_zones.pluck(:id))
                  elsif user.finance_controller?
                    Booking.completed.where(type: ["Survey", "Installation"])
                  end
    apply_filters(assignments)
  end

  def apply_filters(assignments)
    assignments = assignments.where(brand_id: brand_id.split(',')) if brand_id.present?
    assignments = assignments.where(zone_id: zone_id.split(',')) if zone_id.present?
    assignments = assignments.where(brand_model_id: brand_model_id.split(',')) if brand_model_id.present?
    assignments
  end

  def date_range
    (start_date.to_date.in_time_zone("Kolkata").to_time)..(end_date.to_date.in_time_zone("Kolkata").to_time + 1.day)
  end
end
