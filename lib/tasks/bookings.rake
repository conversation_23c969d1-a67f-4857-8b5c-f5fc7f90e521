namespace :bookings do
  desc 'Send notification for next follow up at'
  task notify_follow_up: [:environment] do
    time_now = DateTime.now
    Booking.where(next_follow_up_at: time_now.beginning_of_day..time_now.end_of_day).each do |booking|
      booking.send_follow_up_notification
    end
  end

  desc 'Update Duplicate bookings'
  task modify_booking_code: [:environment] do
    time_now = DateTime.now
    duplicate_ecb_booking_codes = Booking.where("created_at > ? AND type = ?", time_now - 1.day, "Booking").group(:ecb_booking_code).having('COUNT(*) > 1').pluck(:ecb_booking_code)
    prefix = "B-MG7.4WD24OCT"
    running_count = Booking.where('ecb_booking_code LIKE ? AND type = ?', "#{prefix}%", "Booking").order(ecb_booking_code: :desc).first.try(:ecb_booking_code)
    counter = running_count ? running_count.gsub(prefix, '').to_i : 0
    counter += 1
    booking_codes = duplicate_ecb_booking_codes.compact.uniq.select{|x| x.include?(prefix)}
    booking_codes.each do |booking_code|
      Booking.where(type:'Booking', ecb_booking_code: booking_code).all.each do |booking|
        booking.ecb_booking_code = "#{prefix}#{counter}"
        until booking.save do
          counter += 1
        end
        counter += 1
      end
    end
  end
end
