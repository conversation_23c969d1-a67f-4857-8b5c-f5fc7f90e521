namespace :rpush do
  desc 'Create Rpush android app'
  task android_app: [:environment] do
    Rpush::Gcm::App.create(name: 'android_app', connections: 1, environment: Rails.env,
                           type: 'Rpush::Client::ActiveRecord::Gcm::App',
                           auth_key: Rails.application.credentials[Rails.env.to_sym][:fcm_key])
    puts 'Rpush Android app created Successfully'
  end

  desc 'Test Rpush'
  task test: [:environment] do
    messenger = PushMessenger::Gcm.new
    tokens = %w[dYYC8UbjQ6etCO0CtBR1vr:APA91bHXj1mwSOMuf2I9Jq-iQ9T2CWfTyNuVnsdxBLMBcoewSd6b8K0bLJ7ZEanhKHBoO9acz4TyG8WbpZIAQeT67YuYzE0gx2FI_TfTp9ML8CShcvmHm90PhZjileryLcEp1e8NdaUx
                dYYC8UbjQ6etCO0CtBR1vr:APA91bHo3BawuHrxWCvL1921xpZztnO-evIt1Yj_rkxmvWhxUrsWQfZuUS9HA5FTeWhLs0wWMIL_N-pDPnvg6IiMLkReueBR_nMa97XeZwmIIMUetfRQBgqlHMeNEHjwLm2Z9d5kFxyU]
    payload = { title: 'New Survey Assigned', body: 'New Survey Assigned' }
    messenger.deliver(tokens, payload)
    puts 'Rpush Successfully'
  end
end
