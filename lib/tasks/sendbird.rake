namespace :sendbird do
  desc 'Seed Sendbird user data'
  task user_seed: [:environment] do
    User.all.each do |user|
      next if user.sendbird_token.present?

      # user.save_sendbird_user
    end
  end

  desc 'Seed Sendbird channel data'
  task channel_seed: [:environment] do
    Booking.all.each do |booking|
      next if booking.sendbird_channel.present?

      # booking.create_sendbird_channel
    end
  end

  desc 'Update Sendbird channel data'
  task channel_update: [:environment] do
    Booking.all.each do |booking|
      # booking.create_sendbird_channel(true)
    end
  end

  desc 'Remove all users from Sendbird channel'
  task channel_users_remove: [:environment] do
    time = Time.now - (ENV['SENDBIRD_GROUP_VALIDITY'] || 7).to_i.days
    from = time - 1000.days
    to = time
    # bookings = Booking.valid_expired_sendbird_bookings(from, to)
    # bookings.each do |booking|
    #   user_ids = User.where(id: booking.users_for_chat).pluck(:mobile).compact
    #   booking.remove_users_from_chat(user_ids)
    # end
  end

  desc 'Create non-existing Sendbird channel data'
  task channel_fix: [:environment] do
    Booking.where(sendbird_channel: nil).where.not(type: 'Booking').each do |booking|
      # booking.create_sendbird_channel(true)
    end
  end
end
