require 'fcmpush'

namespace :fcm do
  desc 'Test push'
  task test: [:environment] do
    project_id = 'ecb-prod'
    device_tokens = [
      'fgUJW9TcTduf789KQoBGRf:APA91bHbKfXMjHLt9Pg0MvfTTpnW2h_PZXGGwS4PXinwQvgsjTX38nHhHqXdS5aCIBI4TffIyIxfLet-mnnMxEextJqSQ7WLb1NZVIBlyg-HKNFK2q1-kYrxnQsFD6ULEP-VkmNwZ2iF',
      'de5szPhuTaegxS4BQCpXjm:APA91bFpKv7S0OEEUg4TgNuVJHyCzQLTuPbgGxsj3r5XKLmqDiwPYtBYly1hd8Wd-U5lUvihVAqpQLyV78pkz3HgWWM6cmzwzyG8EhBYvitGbiHpQfi6G4Ey7TdQ1ZnSWKJX5gtNjVGi'
    ]
    client = Fcmpush.new(project_id)
    meta = { type: 'Survey', id: 2323, code: 'SDDD' }
    payload = { title: 'Testing FCM', body: 'Received FCM test msg',
                meta: }
    extra_data = payload[:meta]
    extra_data.each_key do |key|
      extra_data[key] = extra_data[key].to_s
    end
    extra_data[:title] = payload[:title]
    extra_data[:body] = payload[:body]
    payloads = device_tokens.map do |token|
      {
        message: {
          token:,
          data: extra_data
        }
      }
    end

    response_array = []
    payloads.each do |payload_single|
      response = client.push(payload_single)
      response_array << response.json
    rescue Fcmpush::ClientError => e
      response_array << JSON.parse(e.response.body)
    end

    puts "Sent all pending notifications... with response: #{response_array}"

    response_array.each_with_index do |result, index|
      if result['error'].present?
        if result['error']['status'] == 'NOT_FOUND'
          puts "Removed registration Id: #{device_tokens[index]}"
        else
          puts "Error sending notification to: #{device_tokens[index]} with error: #{result['error']}"
        end
      end
    end
  end
end
