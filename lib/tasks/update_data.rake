namespace :update_data do
  desc 'brand_booking_code'
  task brand_booking_code: [:environment] do
    Survey.where(brand_booking_code: nil).each do |survey|
      survey.update_column(:brand_booking_code, survey.booking.brand_booking_code)
    end
    Installation.where(brand_booking_code: nil).each do |installation|
      installation.update_column(:brand_booking_code, installation.booking.brand_booking_code)
    end
    puts 'brand_booking_code created Successfully'
  end
end
