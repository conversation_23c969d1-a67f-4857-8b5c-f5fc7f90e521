module ExcelDataGenerator
  class InstallationsReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          "S.NO",
          "Survey Partner Name",
          "Booking Ref Number",
          "Installation Ref Number",
          "Customer city of Survey/Installation",
          "Installation Outstation for Installer Y/N",
          "ECB Person at site",
          "Survey Approval Date",
          "Cable length at Survey",
          "Cable length at Installation",
          "Installer Partner Name",
          "Installation Approval Date",
          "Make of charger",
          "Brand Name",
          "Charger Type",
          "Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation",
          "Core of power cable",
          "Type of power cable",
          "Is there a no show during survey stage ?",
          "Is there a no show during installation stage?",
          "Any extra material used at installation ? if yes then mention",
          "Zone",
          "City Manager",
          "Survey Ref Number",
          "Old Survey Number",
          "Booking Outstation for Installer Y/N",
          "Survey Outstation for Installer Y/N",
          "Installation Outstation for Client Y/N",
          "Booking Outstation for Client Y/N",
          "Survey Outstation for Client Y/N",
          "Survey end Date",
          "Installation end Date",
          "Is this client (OEM) paid - Installation ?",
          "Is this client (OEM) paid - Survey ?",
          "Variant",
          "Brief about installation",
          "Blank Column 1",
          "Blank Column 2",
          "Booking Creation Date",
          "State",
          "Customer Email ID",
          "Nature of work",
          "Old Cable used (length)",
          "New Cable used (length)",
          "No. of old MCB /RCBO used",
          "No. of New MCB /RCBO used",
          "No. of old MCB /RCBO box used",
          "No. of New MCB /RCBO Box used",
          "Remarks (any other material used etc)"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each_with_index do | assignment, index |
        counter = counter + 1
        sheet.add_row [
          counter,
          assignment.survey.survey_partner&.name,
          assignment.booking.ecb_booking_code,
          assignment.ecb_install_code,
          assignment.customer_city,
          assignment.is_outstation ? 'Y' : assignment.is_outstation.nil? ? '' : 'N',
          '',
          assignment&.survey&.approved_at&.to_ist_format,
          assignment.survey&.cable_length_new || assignment.survey&.cable_length,
          assignment.cable_length_new || assignment.cable_length,
          assignment.install_partner&.name,
          assignment&.approved_at&.to_ist_format,
          assignment.charger_make_type,
          assignment.brand.name,
          assignment.obc,
          assignment.cable_gauge,
          assignment.core_of_power_cable,
          assignment.type_of_power_cable,
          assignment.survey.is_no_show_during_survey ? "Y" : "N",
          assignment.is_no_show_during_installation ? "Y" : "N",
          assignment.is_extra_material_used ? "Y" : "N",
          assignment.zone&.name,
          assignment.assigned_city_manager&.name,
          assignment.survey.ecb_survey_code,
          assignment.survey.old_survey_number,
          assignment.booking.is_outstation ? 'Y' : assignment.booking.is_outstation.nil? ? '' : 'N',
          assignment.survey.is_outstation ? 'Y' : assignment.survey.is_outstation.nil? ? '' : 'N',
          assignment.is_outstation_for_client ? 'Y' : assignment.is_outstation_for_client.nil? ? '' : 'N',
          assignment.booking.is_outstation_for_client ? 'Y' : assignment.booking.is_outstation_for_client.nil? ? '' : 'N',
          assignment.survey.is_outstation_for_client ? 'Y' : assignment.survey.is_outstation_for_client.nil? ? '' : 'N',
          assignment.survey.survey_completed_at&.to_ist_format,
          assignment.install_completed_at&.to_ist_format,
          assignment.customer_billing  ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.survey.customer_billing  ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.model_variant,
          '',
          '',
          '',
          assignment.booking.created_at&.to_ist_format,
          assignment.state&.name,
          assignment.email,
          assignment.nature_of_work_string,
          assignment.nature_of_work_reinstallation? ? assignment.old_cable_used_length : '',
          assignment.nature_of_work_reinstallation? ? assignment.new_cable_used_length : '',
          assignment.nature_of_work_reinstallation? ? assignment.number_of_old_mcb_rcbo_used : '',
          assignment.nature_of_work_reinstallation? ? assignment.number_of_new_mcb_rcbo_used : '',
          assignment.nature_of_work_reinstallation? ? assignment.number_of_old_mcb_rcbo_box_used : '',
          assignment.nature_of_work_reinstallation? ? assignment.number_of_new_mcb_rcbo_box_used : '',
          assignment.nature_of_work_reinstallation? ? assignment.nature_of_install_remarks : ''
        ]
      end

      counter
    end
  end
end
