module ExcelDataGenerator
  class DatabaseTwoReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: 'Data') : wb.sheet_by_name('Data')

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          'SN',
          'Brand Name',
          'ZONE',
          'City Manager',
          'Customer Name',
          'Customer Booking Ref No',
          'Booking Date',
          'Model Name',
          'Charger Type',
          'Customer city of Survey/Installation',
          'ECB Booking rcv date',
          "Booking Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Survey Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Installation Status as on #{Date.today.strftime('%d-%m-%Y')}",
          'Booking Creation Date',
          'Installation Outstation for Installer Y/N',
          'Survey end Date',
          'Customer Contact Logs',
          'Booking Ref Number',
          'Survey Ref Number',
          'Old Survey Number',
          'Survey Partner Name',
          'Comments',
          'Make of Charger',
          'Next Follow up date',
          'Installation end Date',
          'Installer Partner Name ',
          'Installation Type',
          'cable length at survey',
          'cable length at installation',
          'Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation',
          'Type of power cable',
          'Core of power cable',
          'Is this client (OEM) paid - Installation ?',
          'Is this client (OEM) paid - Survey ?',
          'Dealer Name',
          'Customer Address 1',
          'PIN Code',
          'Customer Phone Number1',
          'Survey Outstation for Installer Y/N',
          'Confirmed for Survey (Y/N)',
          'Scheduled Date of survey',
          'Broad Survey Findings Stage 1',
          'Broad Survey Findings Stage 2',
          'Date of issue of charger by the dealer to the customer',
          'Scheduled Date of Installation',
          'Customer Email ID',
          'Survey and installation on same day [Y/N]',
          'No show date [ at survey stage ]',
          'No show date [installation stage]',
          'Dealer Location',
          'Dealer Email ID',
          'City Manager Email ID',
          'Zonal Manager Email ID',
          'Variant',
          'State',
          'Customer Phone Number2',
          'Survey Outstation for Client Y/N',
          'Charger Serial No.',
          'Extra Switch gear/Material used at site provided by installer after ECB approval?',
          'VIN ',
          'CEE Name',
          'CEE Contact Date',
          'Expected Delivery Date'
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each do |assignment|
        if assignment.surveys.count <= 1 && assignment.installations.count <= 1
          counter += 1
          sheet.add_row [
            counter,
            assignment.brand.name,
            assignment.zone&.name,
            assignment.assigned_city_manager&.name,
            assignment.customer_name,
            assignment.brand_booking_code,
            assignment.brand_booking_date&.to_ist_format,
            assignment.brand_model&.name,
            assignment.first_installation&.charger_make_type,
            assignment.first_survey&.customer_city,
            assignment.ecb_receive_date&.to_ist_format,
            assignment.status_text,
            assignment.first_survey&.status_text,
            assignment.first_installation&.status_text,
            assignment.created_at&.to_ist_format,
            if assignment.first_installation&.is_outstation_for_client
              'Y'
            else
              assignment.first_installation&.is_outstation_for_client.nil? ? '' : 'N'
            end,
            assignment.first_survey&.survey_completed_at&.to_ist_format,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(', '),
            assignment.ecb_booking_code,
            assignment.first_survey&.ecb_survey_code,
            assignment.first_survey&.old_survey_number,
            assignment.first_survey&.survey_partner&.name,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.last&.second&.capitalize&.gsub('_', ' '),
            assignment.first_installation&.charger_make_type,
            future_next_follow_up_at(assignment, assignment.first_survey, assignment.first_installation)&.to_ist_format,
            assignment.first_installation&.install_completed_at&.to_ist_format,
            assignment.first_installation&.install_partner&.name,
            assignment.first_installation&.install_type_string,
            assignment.first_survey&.cable_length_new || assignment.first_survey&.cable_length,
            assignment.first_installation&.status_cancelled? ? '' : assignment.first_installation&.cable_length_new || assignment.first_installation&.cable_length,
            assignment.first_installation&.cable_gauge,
            assignment.first_installation&.type_of_power_cable,
            assignment.first_installation&.core_of_power_cable,
            if assignment.first_installation
              assignment.first_installation.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid'
            else
              ''
            end,
            if assignment.first_survey
              assignment.first_survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid'
            else
              ''
            end,
            assignment.dealer_name,
            assignment.first_survey&.address_line1,
            assignment.first_survey&.pincode || assignment.pincode,
            assignment.contact_number,
            if assignment.first_survey&.is_outstation
              'Y'
            else
              assignment.first_survey&.is_outstation.nil? ? '' : 'N'
            end,
            assignment.first_survey&.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
            assignment.first_survey&.scheduled_survey_date_time&.to_ist_format,
            assignment.first_survey&.summary_stage1,
            assignment.first_survey&.summary_stage2,
            assignment.charger_issued_date || assignment.first_installation&.charger_issued_date,
            assignment.first_installation&.scheduled_install_date_time&.to_ist_format,
            assignment.email,
            '',
            '',
            '',
            assignment.dealer_location,
            assignment.dealer_email,
            assignment.city_manager_email,
            assignment.zonal_manager_email,
            assignment.model_variant,
            assignment.state&.name,
            assignment.alt_contact_number,
            if assignment.first_survey&.is_outstation_for_client
              'Y'
            else
              assignment.first_survey&.is_outstation_for_client.nil? ? '' : 'N'
            end,
            assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
            '',
            assignment.first_installation&.vin,
            assignment.cee_name,
            assignment.cee_contact_date,
            assignment.expected_delivery_date
          ], types: column_types
        else
          assignment.surveys.each do |survey|
            if survey.installations.count == 0
              counter += 1
              sheet.add_row [
                counter,
                assignment.brand.name,
                assignment.zone&.name,
                assignment.assigned_city_manager&.name,
                assignment.customer_name,
                assignment.brand_booking_code,
                assignment.brand_booking_date&.to_ist_format,
                assignment.brand_model&.name,
                assignment.first_installation&.charger_make_type,
                survey&.customer_city,
                assignment.ecb_receive_date&.to_ist_format,
                assignment.status_text,
                survey&.status_text,
                assignment.first_installation&.status_text,
                assignment.created_at&.to_ist_format,
                if assignment.first_installation&.is_outstation_for_client
                  'Y'
                else
                  assignment.first_installation&.is_outstation_for_client.nil? ? '' : 'N'
                end,
                survey&.survey_completed_at&.to_ist_format,
                (assignment.customer_connect_logs_remarks + (survey ? survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(', '),
                assignment.ecb_booking_code,
                survey&.ecb_survey_code,
                survey&.old_survey_number,
                survey&.survey_partner&.name,
                (assignment.customer_connect_logs_remarks + (survey ? survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.last&.second&.capitalize&.gsub('_', ' '),
                assignment.first_installation&.charger_make_type,
                future_next_follow_up_at(assignment, survey, nil)&.to_ist_format,
                assignment.first_installation&.install_completed_at&.to_ist_format,
                assignment.first_installation&.install_partner&.name,
                assignment.first_installation&.install_type_string,
                survey&.cable_length_new || survey&.cable_length,
                assignment.first_installation&.status_cancelled? ? '' : assignment.first_installation&.cable_length_new || assignment.first_installation&.cable_length,
                assignment.first_installation&.cable_gauge,
                assignment.first_installation&.type_of_power_cable,
                assignment.first_installation&.core_of_power_cable,
                assignment.first_installation&.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                assignment.dealer_name,
                survey&.address_line1,
                survey.pincode,
                assignment.contact_number,
                if survey&.is_outstation
                  'Y'
                else
                  survey&.is_outstation.nil? ? '' : 'N'
                end,
                survey&.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
                survey&.scheduled_survey_date_time&.to_ist_format,
                survey&.summary_stage1,
                survey&.summary_stage2,
                assignment.charger_issued_date,
                assignment.first_installation&.scheduled_install_date_time&.to_ist_format,
                assignment.email,
                '',
                '',
                '',
                assignment.dealer_location,
                assignment.dealer_email,
                assignment.city_manager_email,
                assignment.zonal_manager_email,
                assignment.model_variant,
                assignment.state&.name,
                assignment.alt_contact_number,
                if survey&.is_outstation_for_client
                  'Y'
                else
                  survey&.is_outstation_for_client.nil? ? '' : 'N'
                end,
                assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
                '',
                assignment.first_installation&.vin,
                assignment.cee_name,
                assignment.cee_contact_date,
                assignment.expected_delivery_date
              ], types: column_types
            else
              survey.installations.each do |installation|
                counter += 1
                sheet.add_row [
                  counter,
                  assignment.brand.name,
                  assignment.zone&.name,
                  assignment.assigned_city_manager&.name,
                  assignment.customer_name,
                  assignment.brand_booking_code,
                  assignment.brand_booking_date&.to_ist_format,
                  assignment.brand_model&.name,
                  installation&.charger_make_type,
                  survey&.customer_city,
                  assignment.ecb_receive_date&.to_ist_format,
                  assignment.status_text,
                  survey&.status_text,
                  installation&.status_text,
                  assignment.created_at&.to_ist_format,
                  if installation&.is_outstation_for_client
                    'Y'
                  else
                    installation&.is_outstation_for_client.nil? ? '' : 'N'
                  end,
                  survey&.survey_completed_at&.to_ist_format,
                  (assignment.customer_connect_logs_remarks + (survey ? survey.customer_connect_logs_remarks : []) + (installation ? installation.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(', '),
                  assignment.ecb_booking_code,
                  survey&.ecb_survey_code,
                  survey&.old_survey_number,
                  survey&.survey_partner&.name,
                  (assignment.customer_connect_logs_remarks + (survey ? survey.customer_connect_logs_remarks : []) + (installation ? installation.customer_connect_logs_remarks : [])).sort.last&.second&.capitalize&.gsub('_', ' '),
                  installation.charger_make_type,
                  future_next_follow_up_at(assignment, survey, installation)&.to_ist_format,
                  installation&.install_completed_at&.to_ist_format,
                  installation&.install_partner&.name,
                  installation&.install_type_string,
                  survey&.cable_length_new || survey&.cable_length,
                  installation&.status_cancelled? ? '' : installation&.cable_length_new || installation&.cable_length,
                  installation&.cable_gauge,
                  installation&.type_of_power_cable,
                  installation&.core_of_power_cable,
                  installation&.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                  survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                  assignment.dealer_name,
                  survey&.address_line1,
                  survey.pincode,
                  assignment.contact_number,
                  if survey&.is_outstation
                    'Y'
                  else
                    survey&.is_outstation.nil? ? '' : 'N'
                  end,
                  survey&.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
                  survey&.scheduled_survey_date_time&.to_ist_format,
                  survey&.summary_stage1,
                  survey&.summary_stage2,
                  assignment.charger_issued_date || installation.charger_issued_date,
                  installation&.scheduled_install_date_time&.to_ist_format,
                  assignment.email,
                  '',
                  '',
                  '',
                  assignment.dealer_location,
                  assignment.dealer_email,
                  assignment.city_manager_email,
                  assignment.zonal_manager_email,
                  assignment.model_variant,
                  assignment.state&.name,
                  assignment.alt_contact_number,
                  if survey&.is_outstation_for_client
                    'Y'
                  else
                    survey&.is_outstation_for_client.nil? ? '' : 'N'
                  end,
                  installation&.charger_serial_number ? " #{installation&.charger_serial_number}" : '',
                  '',
                  installation&.vin,
                  assignment.cee_name,
                  assignment.cee_contact_date,
                  assignment.expected_delivery_date
                ], types: column_types
              end
            end
          end
        end
      end

      counter
    end

    def future_next_follow_up_at(booking, survey, installation)
      follow_ups = [booking&.next_follow_up_at, survey&.next_follow_up_at, installation&.next_follow_up_at].compact
      future_follow_ups = follow_ups.select { |follow_up| follow_up > time_now }
      future_follow_ups.min
    end

    def time_now
      @time_now ||= Time.now
    end
  end
end
