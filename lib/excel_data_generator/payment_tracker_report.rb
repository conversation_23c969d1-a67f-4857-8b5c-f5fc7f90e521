module ExcelDataGenerator
  class PaymentTrackerReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        sheet.add_row [
          "SN",
          "Brand Name",
          "Zone",
          "Dealer Name",
          "Dealer Location",
          "Customer Name",
          "Booking Date",
          "Survey Approval Date",
          "Installation Approval Date",
          "Survey Outstation for Installer Y/N",
          "Booking Outstation for Installer Y/N",
          "Installation Outstation for Installer Y/N",
          "Survey Outstation for Client Y/N",
          "Booking Outstation for Client Y/N",
          "Installation Outstation for Client Y/N",
          "Customer Address 1",
          "Customer city of Survey/Installation",
          "PIN Code",
          "Customer Phone Number 1",
          "PAYMENT PARTY CODE",
          "Customer Email Id",
          "Survey end Date",
          "Survey Ref Number",
          "City Manager",
          "Cable length at Survey",
          "Cable length at Installation",
          "Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation",
          "cable route is overground or underground",
          "Payment amount [from the email sent to the customer by business leader]",
          "Payment rcd date",
          "Payment rcd amount",
          "Installation end Date",
          "Installation Type",
          "Remarks related to payment [extra payment/refund / final cable length (CL) etc etc]",
          "Is this client (OEM) paid ?",
          "Charger Type",
          "Booking Creation Date"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each_with_index do | assignment, index |
        counter = counter + 1
        sheet.add_row [
          counter,
          assignment.brand.name,
          assignment.zone&.name,
          assignment.dealer_name,
          assignment.dealer_location,
          assignment.customer_name,
          assignment.brand_booking_date&.to_ist_format,
          assignment&.approved_at&.to_ist_format,
          assignment&.first_installation&.approved_at&.to_ist_format,
          assignment.is_outstation ? "Y" : assignment.is_outstation.nil? ? '' : "N",
          assignment.booking.is_outstation ? "Y" : assignment.booking.is_outstation.nil? ? '' : "N",
          assignment.first_installation&.is_outstation ? "Y" : assignment.first_installation&.is_outstation.nil? ? '' : "N",
          assignment.is_outstation_for_client ? "Y" : assignment.is_outstation_for_client.nil? ? '' : "N",
          assignment.booking.is_outstation_for_client ? "Y" : assignment.booking.is_outstation_for_client.nil? ? '' : "N",
          assignment.first_installation&.is_outstation_for_client ? "Y" : assignment.first_installation&.is_outstation_for_client.nil? ? '' : "N",
          "#{assignment.address_line1} #{assignment.address_line2}",
          assignment.city.name,
          assignment.pincode,
          assignment.contact_number,
          '',
          assignment.email,
          assignment.survey_completed_at&.to_ist_format,
          assignment.ecb_survey_code,
          assignment.assigned_city_manager&.name,
          assignment.cable_length_new || assignment.cable_length,
          assignment.finished_installation&.cable_length_new || assignment.finished_installation&.cable_length,
          assignment.finished_installation&.cable_gauge,
          '',
          '',
          '',
          '',
          assignment.finished_installation&.install_completed_at&.to_ist_format ,
          assignment.finished_installation&.install_type_string  ,
          '',
          assignment.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.obc,
          assignment.booking.created_at&.to_ist_format
        ]
      end

      counter
    end
  end
end
