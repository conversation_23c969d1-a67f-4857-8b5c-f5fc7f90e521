module ExcelDataGenerator
  class ClientDatabaseReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          "SN",
          "Brand Name",
          "ZONE",
          "Dealer Name",
          "Dealer Location",
          "Customer Name",
          "Customer Booking Ref No",
          "Booking Date",
          "Model Name",
          "Variant",
          "Charger Type",
          "Customer Address 1",
          "Customer Address2",
          "Customer city of Survey/Installation",
          "PIN Code",
          "State",
          "Customer Phone Number1",
          "Customer Phone Number2",
          "ECB Booking rcv date",
          "Booking Creation Date",
          "Survey Approval Date",
          "Installation Approval Date",
          "Customer Contact Logs",
          "Booking Ref Number",
          "Survey Ref Number",
          "Broad Survey Findings Stage 1",
          "Comments",
          "Installation Ref Number",
          "Make of Charger",
          "Customer Email ID",
          "Charger Serial No.",
          "VIN ",
          "Commission Number"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each do | assignment |
        if assignment.surveys.count <= 1 && assignment.installations.count <= 1
          counter = counter + 1
          sheet.add_row [
            counter,
            assignment.brand.name,
            assignment.zone&.name,
            assignment.dealer_name,
            assignment.dealer_location,
            assignment.customer_name,
            assignment.brand_booking_code,
            assignment.brand_booking_date&.to_ist_format,
            assignment.brand_model&.name,
            assignment.model_variant,
            assignment.obc,
            assignment.first_survey&.address_line1,
            assignment.first_survey&.address_line2,
            assignment.first_survey&.customer_city,
            assignment.first_survey&.pincode || assignment.pincode,
            assignment.state&.name,
            assignment.contact_number,
            assignment.alt_contact_number,
            assignment.ecb_receive_date&.to_ist_format,
            assignment.created_at&.to_ist_format,
            assignment.first_survey&.approved_at&.to_ist_format,
            assignment.first_installation&.approved_at&.to_ist_format,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(", "),
            assignment.ecb_booking_code,
            assignment.first_survey&.ecb_survey_code,
            assignment.first_survey&.summary_stage1,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.last&.second&.capitalize&.gsub('_', ' '),
            assignment.first_installation&.ecb_install_code,
            assignment.first_installation&.charger_make_type,
            assignment.email,
            assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
            assignment.first_installation&.vin,
            assignment.first_installation&.commission_number
          ], types: column_types
        else
          assignment.surveys.each do | survey |
            if survey.installations.count == 0
              counter = counter + 1
              sheet.add_row [
                counter,
                assignment.brand.name,
                assignment.zone&.name,
                assignment.dealer_name,
                assignment.dealer_location,
                assignment.customer_name,
                assignment.brand_booking_code,
                assignment.brand_booking_date&.to_ist_format,
                assignment.brand_model&.name,
                assignment.model_variant,
                assignment.obc,
                survey.address_line1,
                survey.address_line2,
                survey.customer_city,
                survey.pincode,
                assignment.state&.name,
                assignment.contact_number,
                assignment.alt_contact_number,
                assignment.ecb_receive_date&.to_ist_format,
                survey.booking&.created_at&.to_ist_format,
                survey&.approved_at&.to_ist_format,
                '',
                (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks ).sort.reverse.map(&:last).join(", "),
                assignment.ecb_booking_code,
                survey.ecb_survey_code,
                survey.summary_stage1,
                (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks ).sort.last&.second&.capitalize&.gsub('_', ' '),
                '',
                '',
                assignment.email,
                '',
                '',
                ''
              ], types: column_types
            else
              survey.installations.each do | installation |
                counter = counter + 1
                sheet.add_row [
                  counter,
                  assignment.brand.name,
                  assignment.zone&.name,
                  assignment.dealer_name,
                  assignment.dealer_location,
                  assignment.customer_name,
                  assignment.brand_booking_code,
                  assignment.brand_booking_date&.to_ist_format,
                  assignment.brand_model&.name,
                  assignment.model_variant,
                  assignment.obc,
                  survey.address_line1,
                  survey.address_line2,
                  installation.customer_city,
                  survey.pincode,
                  assignment.state&.name,
                  assignment.contact_number,
                  assignment.alt_contact_number,
                  assignment.ecb_receive_date&.to_ist_format,
                  installation.booking&.created_at&.to_ist_format,
                  installation&.survey&.approved_at&.to_ist_format,
                  installation&.approved_at&.to_ist_format,
                  (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks ).sort.reverse.map(&:last).join(', '),
                  assignment.ecb_booking_code,
                  survey.ecb_survey_code,
                  survey.summary_stage1,
                  (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks ).sort.last&.second&.capitalize&.gsub('_', ' '),
                  installation.ecb_install_code,
                  installation.charger_make_type,
                  assignment.email,
                  assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
                  installation.vin,
                  installation.commission_number
                ], types: column_types
              end
            end
          end
        end
      end

      counter
    end
  end
end
