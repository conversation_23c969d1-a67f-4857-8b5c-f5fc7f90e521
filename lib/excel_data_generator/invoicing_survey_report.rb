module ExcelDataGenerator
  class InvoicingSurveyReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        sheet.add_row [
          "S.no",
          "Brand Name",
          "Zone",
          "Dealer Name",
          "Customer Name",
          "Customer Email ID",
          "Customer Booking Ref No",
          "Booking Date",
          "Model Name",
          "Variant",
          "Survey being done for Charger type ?",
          "Customer Address 1",
          "Customer city of Survey/Installation",
          "Customer Phone Number 1",
          "Survey Approval Date",
          "Booking Ref Number",
          "Survey Ref Number",
          "Old Survey Number",
          "Broad Survey Findings Stage 1",
          "Broad Survey Findings Stage 2",
          "City Manager",
          "Survey end Date",
          "Installation Approval Date",
          "Survey Partner Name",
          "Cable length at survey",
          "Surveyor Name",
          "Charger Type",
          "Is this client (OEM) paid - Survey ?",
          "Customer Address 2",
          "PIN Code",
          "Customer Phone Number 2",
          "Survey Outstation for Installer Y/N",
          "Booking Outstation for Installer Y/N",
          "Installation Outstation for Installer Y/N",
          "Survey Outstation for Client Y/N",
          "Booking Outstation for Client Y/N",
          "Installation Outstation for Client Y/N",
          "Booking Creation Date",
          "State"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each_with_index do | assignment, index |
        counter = counter + 1
        sheet.add_row [
          counter,
          assignment.brand.name,
          assignment.zone&.name,
          assignment.dealer_name,
          assignment.customer_name,
          assignment.email,
          assignment.brand_booking_code,
          assignment.brand_booking_date&.to_ist_format,
          assignment.booking.brand_model&.name,
          assignment.model_variant,
          assignment.survey_for_charger_type,
          assignment.address_line1,
          assignment.customer_city,
          assignment.contact_number,
          assignment&.approved_at&.to_ist_format,
          assignment.booking.ecb_booking_code,
          assignment.ecb_survey_code,
          assignment.old_survey_number,
          assignment.summary_stage1,
          assignment.summary_stage2,
          assignment.assigned_city_manager&.name,
          assignment.survey_completed_at&.to_ist_format,
          assignment&.first_installation&.approved_at&.to_ist_format,
          assignment.survey_partner&.name,
          assignment.cable_length_new || assignment.cable_length,
          assignment.surveyor_name,
          assignment.obc,
          assignment.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.address_line2,
          assignment.pincode,
          assignment.alt_contact_number,
          assignment.is_outstation ? "Y" : assignment.is_outstation.nil? ? '' : "N",
          assignment.booking.is_outstation ? "Y" : assignment.booking.is_outstation.nil? ? '' : "N",
          assignment.first_installation&.is_outstation ? "Y" : assignment.first_installation&.is_outstation.nil? ? '' : "N",
          assignment.is_outstation_for_client ? "Y" : assignment.is_outstation_for_client.nil? ? '' : "N",
          assignment.booking.is_outstation_for_client ? "Y" : assignment.booking.is_outstation_for_client.nil? ? '' : "N",
          assignment.first_installation&.is_outstation_for_client ? "Y" : assignment.first_installation&.is_outstation_for_client.nil? ? '' : "N",
          assignment.booking.created_at&.to_ist_format,
          assignment.state.name
        ]
      end

      counter
    end
  end
end
