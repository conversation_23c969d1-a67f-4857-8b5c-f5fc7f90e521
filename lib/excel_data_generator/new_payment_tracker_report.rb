module ExcelDataGenerator
  class NewPaymentTrackerReport
    PAYMENT_AND_EMAIL_STATUS_AVAILABLE_FROM = "2025-03-01"

    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        sheet.add_row [
          "SN",
          "Brand Name",
          "Zone",
          "City Manager",
          "Dealer Name",
          "Dealer Location",
          "Dealer Email ID",
          "City Manager Email ID",
          "Zonal Manager Email ID",
          "Customer Name",
          "Customer Booking Ref No",
          "Booking Date",
          "Customer Address 1",
          "Customer city of Survey/Installation",
          "PIN Code",
          "Customer State",
          "Customer Phone Number 1",
          "PAYMENT PARTY CODE",
          "PAYMENT GATEWAY ID",
          "CUSTOMER VPA",
          "CUSTOMER BANK",
          "Customer Email Id",
          "Survey Approval Date",
          "Booking Ref Number",
          "Survey Ref Number",
          "Old Survey Number",
          "Charger Type",
          "Cable length at Survey",
          "Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation",
          "cable route is overground or underground",
          "Payment amount [from the email sent to the customer by business leader]",
          "Installation Approval Date",
          "Installation Type",
          "Cable length at Installation",
          "Description as per bank statement(For First Payment)",
          "First Payment received date",
          "Received amount (First)",
          "Description as per bank statement(For Second Payment)",
          "Second Payment received date",
          "Received amount (Second)",
          "Description as per bank statement(For Third Payment)",
          "Third Payment received date",
          "Received amount (Third)",
          "Total Amount Received",
          "Refund Description as per bank statement (if any)",
          "Invoice No.",
          "Invoice date",
          "Invoice Amount",
          "Invoice Remarks",
          "refund amount (if any)",
          "refund date",
          "refund status",
          "Remarks related to payment [extra payment/refund / final cable length (CL) etc etc]",
          "Is this client (OEM) paid - Installation ?",
          "Is this client (OEM) paid - Survey ?",
          "Charger Make",
          "Brand model variant",
          "Booking Creation Date",
          "Payment seeking email sent (Yes/No)",
          "Refund email sent (Yes/No)",
          "Refund processed email sent (Yes/No)",
          "Customer GST",
          "Dealer GST"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each_with_index do | assignment, index |
        counter = counter + 1
        sheet.add_row [
          counter,
          assignment.brand.name,
          assignment.zone&.name,
          assignment.assigned_city_manager&.name,
          assignment.dealer_name,
          assignment.dealer_location,
          assignment.booking.dealer_email,
          assignment.booking.city_manager_email,
          assignment.booking.zonal_manager_email,
          assignment.customer_name,
          assignment.brand_booking_code,
          assignment.brand_booking_date&.to_ist_format,
          "#{assignment.address_line1} #{assignment.address_line2}",
          assignment.customer_city,
          assignment.pincode,
          assignment.state&.name,
          assignment.contact_number,
          assignment.first_payment&.payment_party_code,
          assignment.first_payment&.payment_gateway_id,
          assignment.first_payment&.customer_vpa,
          assignment.first_payment&.customer_bank,
          assignment.email,
          assignment&.approved_at&.to_ist_format,
          assignment.booking.ecb_booking_code,
          assignment.ecb_survey_code,
          assignment.old_survey_number,
          assignment.obc,
          assignment.cable_length_new || assignment.cable_length,
          assignment.finished_installation&.cable_gauge,
          '',
          assignment.invoice_total,
          assignment.first_installation&.approved_at&.to_ist_format,
          assignment.finished_installation&.install_type_string,
          assignment.finished_installation&.cable_length_new || assignment.finished_installation&.cable_length,
          assignment.first_payment&.payment_report_description,
          assignment.first_payment&.payment_date,
          assignment.first_payment&.payment_amount,
          assignment.second_payment&.payment_report_description,
          assignment.second_payment&.payment_date,
          assignment.second_payment&.payment_amount,
          assignment.third_payment&.payment_report_description,
          assignment.third_payment&.payment_date,
          assignment.third_payment&.payment_amount,
          assignment.total_payment_amount.to_f,
          assignment.first_refund&.payment_description,
          assignment.invoice_number,
          assignment.invoice_date,
          assignment.invoice_amount,
          assignment.invoice_remarks,
          assignment.total_refund_amount,
          refund_date(assignment),
          refund_status(assignment),
          assignment.payment_remarks,
          assignment.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.charger_make_type || assignment.charger_make_type,
          assignment.model_variant,
          assignment.booking.created_at&.to_ist_format,
          seek_payment_email_sent?(assignment),
          refund_email_sent?(assignment),
          refund_processed_email_sent?(assignment),
          assignment.booking.gst,
          assignment.booking.dealer_gst
        ]
      end

      counter
    end

    def refund_status(survey)
      # Keeping old logic for past data
      return  survey.first_refund.present? ? 'done' : '' if survey.created_at < PAYMENT_AND_EMAIL_STATUS_AVAILABLE_FROM.to_date

      return '' unless survey.status_completed?
      return 'pending' unless survey.payment_received?

      'done'
    end

    def seek_payment_email_sent?(survey)
      return '' if survey.created_at < PAYMENT_AND_EMAIL_STATUS_AVAILABLE_FROM.to_date
      return 'No' unless survey.seek_payment_email_sent?

      'Yes'
    end

    def refund_email_sent?(survey)
      return '' if survey.created_at < PAYMENT_AND_EMAIL_STATUS_AVAILABLE_FROM.to_date
      return 'Yes' if survey.refund_email_sent?

      'No'
    end

    def refund_processed_email_sent?(survey)
      return '' if survey.created_at < PAYMENT_AND_EMAIL_STATUS_AVAILABLE_FROM.to_date
      return 'Yes' if survey.refund_processed_email_sent?

      'No'
    end

    def refund_date(survey)
      # Keeping old logic for past data
      return  survey.first_refund.present? ? survey.first_refund.payment_date : '' if survey.created_at < PAYMENT_AND_EMAIL_STATUS_AVAILABLE_FROM.to_date

      return '' unless survey.status_completed?
      return '' unless survey.payment_received?

      survey.first_refund&.payment_date
    end
  end
end
