module ExcelDataGenerator
  class InvoicingInstallationReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          "SN",
          "Brand Name",
          "Zone",
          "Dealer Name",
          "Customer Name",
          "Customer Booking Ref No",
          "Booking Date",
          "Model Name",
          "Variant",
          "Charger Type",
          "Customer city of Survey/Installation",
          "Survey Ref Number",
          "Old Survey Number",
          "Survey Approval Date",
          "Installation Approval Date",
          "Make of Charger",
          "Cable Length at Installation",
          "Cable Length at Survey",
          "Charger Serial No.",
          "VIN",
          "Commissioning Done (Yes/N0)",
          "Commission Number",
          "Installation Type",
          "Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation",
          "City Manager",
          "Survey end Date",
          "Installation end Date",
          "Booking Ref Number",
          "Installation Ref Number",
          "Installation Outstation for Installer Y/N",
          "Booking Outstation for Installer Y/N",
          "Survey Outstation for Installer Y/N",
          "Installation Outstation for Client Y/N",
          "Booking Outstation for Client Y/N",
          "Survey Outstation for Client Y/N",
          "Is this client (OEM) paid - Installation ?",
          "Survey Partner Name",
          "Installer Partner Name",
          "Survey being done for Charger type ?",
          "Inv number in which this survey was claimed",
          "Brief about installation",
          "Booking Creation Date",
          "Customer Address 1",
          "Customer Address 2",
          "Customer Phone Number 1",
          "Customer Phone Number 2",
          "PIN Code",
          "Customer Email ID",
          "State",
          "Nature of work",
          "Commission date"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each_with_index do | assignment, index |
        counter = counter + 1
        sheet.add_row [
          counter,
          assignment.brand.name,
          assignment.zone&.name,
          assignment.dealer_name,
          assignment.customer_name,
          assignment.brand_booking_code,
          assignment.brand_booking_date&.to_ist_format,
          assignment.booking.brand_model&.name,
          assignment.model_variant,
          assignment.obc,
          assignment.customer_city,
          assignment.survey.ecb_survey_code,
          assignment.survey.old_survey_number,
          assignment&.survey&.approved_at&.to_ist_format,
          assignment&.approved_at&.to_ist_format,
          assignment.charger_make_type,
          assignment.cable_length_new || assignment.cable_length,
          assignment.survey.cable_length_new || assignment.survey.cable_length,
          assignment.charger_serial_number ? " #{assignment.charger_serial_number}" : '',
          assignment.booking.vin || assignment.vin,
          assignment.commissioning_string,
          assignment.commission_number,
          assignment.install_type_string,
          assignment.cable_gauge,
          assignment.assigned_city_manager&.name,
          assignment.survey.survey_completed_at&.to_ist_format,
          assignment.install_completed_at&.to_ist_format,
          assignment.booking.ecb_booking_code,
          assignment.ecb_install_code,
          assignment.is_outstation ? "Y" : assignment.is_outstation.nil? ? '' : "N",
          assignment.booking.is_outstation ? "Y" : assignment.booking.is_outstation.nil? ? '' : "N",
          assignment.survey.is_outstation ? "Y" : assignment.survey.is_outstation.nil? ? '' : "N",
          assignment.is_outstation_for_client ? "Y" : assignment.is_outstation_for_client.nil? ? '' : "N",
          assignment.booking.is_outstation_for_client ? "Y" : assignment.booking.is_outstation_for_client.nil? ? '' : "N",
          assignment.survey.is_outstation_for_client ? "Y" : assignment.survey.is_outstation_for_client.nil? ? '' :  "N",
          assignment.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
          assignment.survey_partner&.name,
          assignment.install_partner&.name,
          assignment.obc,
          '',
          '',
          assignment.booking.created_at&.to_ist_format,
          assignment.address_line1,
          assignment.address_line2,
          assignment.contact_number,
          assignment.alt_contact_number,
          assignment.survey.pincode,
          assignment.email,
          assignment.survey.state.name,
          assignment.nature_of_work_string,
          assignment.commission_date&.to_ist_format
        ], types: column_types
      end

      counter
    end
  end
end
