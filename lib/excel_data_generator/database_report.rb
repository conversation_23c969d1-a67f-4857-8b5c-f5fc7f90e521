module ExcelDataGenerator
  class DatabaseReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: 'Data') : wb.sheet_by_name('Data')

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          'SN',
          'Brand Name',
          'ZONE',
          'City Manager',
          'Dealer Name',
          'Dealer Location',
          'Dealer Email ID',
          'City Manager Email ID',
          'Zonal Manager Email ID',
          'Customer Name',
          'Customer Booking Ref No',
          'Booking Date',
          'Brand booking status',
          "Survey Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Installation Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Booking Status as on #{Date.today.strftime('%d-%m-%Y')}",
          'Model Name',
          'Variant',
          'Colour',
          'Charger Type',
          'Customer Address 1',
          'Customer Address2',
          'Customer city of Survey/Installation',
          'EV city',
          'PIN Code',
          'State',
          'Customer Phone Number1',
          'Customer Phone Number2',
          'ECB Booking rcv date',
          'Booking Creation Date',
          'Survey Creation Date',
          'Installation Creation Date',
          'Survey Approval Date',
          'Installation Approval Date',
          'Customer Contact Logs',
          'Booking Outstation for Installer Y/N',
          'Survey Outstation for Installer Y/N',
          'Installation Outstation for Installer Y/N',
          'Booking Outstation for Client Y/N',
          'Survey Outstation for Client Y/N',
          'Installation Outstation for Client Y/N',
          'Confirmed for Survey (Y/N)',
          'Customer Ok with call recording [Y/N]',
          'Scheduled Date of survey',
          'Survey end Date',
          'Booking Ref Number',
          'Survey Ref Number',
          'Old Survey Number',
          'Survey Partner Name',
          'Broad Survey Findings Stage 1',
          'Broad Survey Findings Stage 2',
          'Comments',
          'Installation Ref Number',
          'Date of issue of charger by the dealer to the customer',
          'Make of Charger',
          'Scheduled Date of Installation',
          'Installation end Date',
          'Installer Partner Name ',
          'Customer Email ID',
          'Installation Type',
          'Charger Serial No.',
          'Attributes',
          'Next Follow up date',
          'cable length at survey',
          'cable length at installation',
          'Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation',
          'Survey and installation on same day [Y/N]',
          'No show date [ at survey stage ]',
          'No show comments',
          'No show date [installation stage]',
          'No show comments',
          'BHE',
          'BHE Date',
          'Installation addresss [if different from the customer address mentioned in Column L]',
          'Extra Switch gear/Material used at site provided by installer after ECB approval?',
          'Final Status',
          'VIN ',
          'Commission Number',
          'CFF:Q1: Overall satisfaction',
          'CFF:Q6: Any open issues',
          'Total Sanctioned Load',
          'Average Load Utilised',
          'Survey Customer Rating',
          'Installation Customer Rating',
          'Booking Uploaded By',
          'Type of power cable',
          'Core of power cable',
          'MCB used on site',
          'MCB box used on site',
          'Is this client (OEM) paid - Installation ?',
          'Is this client (OEM) paid - Survey ?',
          'Is there a no show during survey stage ?',
          'Is there a no show during installation stage ?',
          'Any extra material used at installation ? if yes then mention',
          'Surveyor Name',
          'First Contact Date',
          'CEE Name',
          'CEE Contact Date',
          'Nature of work',
          'Expected Delivery Date',
          "Date on which customer informed us about their readiness for survey",
          "Status of customer side SOW",
          "Date of completion of SOW",
          "Status of payment for extra cable",
          "Date of payment for extra cable if applicable",
          "Date on which customer informed us about their readiness for installation",
          "Date on which the Booking status has been updated",
          "Date on which the EDD status has been updated",
          "Customer GST",
          "Dealer GST",
          "Customer proposed date for survey",
          "Date of updation of proposed survey date",
          "Customer proposed date for installation",
          "Date of updation of proposed installation date",
          'Last Updated'
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each do |assignment|
        if assignment.surveys.size <= 1 && assignment.installations.size <= 1
          counter += 1

          sheet.add_row [
            counter,
            assignment.brand.name,
            assignment.zone&.name,
            assignment.assigned_city_manager&.name,
            assignment.dealer_name,
            assignment.dealer_location,
            assignment.dealer_email,
            assignment.city_manager_email,
            assignment.zonal_manager_email,
            assignment.customer_name,
            assignment.brand_booking_code,
            assignment.brand_booking_date&.to_ist_format,
            assignment.brand_booking_status,
            assignment.first_survey&.status_text,
            assignment.first_installation&.status_text,
            assignment.status_text,
            assignment.brand_model&.name,
            assignment.first_survey&.model_variant || assignment.model_variant,
            assignment.model_colour,
            assignment.first_survey&.obc || assignment.obc,
            assignment.first_survey&.address_line1,
            assignment.first_survey&.address_line2,
            assignment.first_survey&.customer_city,
            assignment.city&.name,
            assignment.first_survey&.pincode || assignment.pincode,
            assignment.state&.name,
            assignment.contact_number,
            assignment.alt_contact_number,
            assignment.ecb_receive_date&.to_ist_format,
            assignment.created_at&.to_ist_format,
            assignment.first_survey&.created_at&.to_ist_format,
            assignment.first_installation&.created_at&.to_ist_format,
            assignment.first_survey&.approved_at&.to_ist_format,
            assignment.first_installation&.approved_at&.to_ist_format,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(', '),
            if assignment.is_outstation
              'Y'
            else
              assignment.is_outstation.nil? ? '' : 'N'
            end,
            if assignment.first_survey&.is_outstation
              'Y'
            else
              assignment.first_survey&.is_outstation.nil? ? '' : 'N'
            end,
            if assignment.first_installation&.is_outstation
              'Y'
            else
              assignment.first_installation&.is_outstation.nil? ? '' : 'N'
            end,
            if assignment.is_outstation_for_client
              'Y'
            else
              assignment.is_outstation_for_client.nil? ? '' : 'N'
            end,
            if assignment.first_survey&.is_outstation_for_client
              'Y'
            else
              assignment.first_survey&.is_outstation_for_client.nil? ? '' : 'N'
            end,
            if assignment.first_installation&.is_outstation_for_client
              'Y'
            else
              assignment.first_installation&.is_outstation_for_client.nil? ? '' : 'N'
            end,
            assignment.first_survey&.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
            assignment.customer_ok_with_recording ? 'Y' : 'N',
            assignment.first_survey&.scheduled_survey_date_time&.to_ist_format,
            assignment.first_survey&.survey_completed_at&.to_ist_format,
            assignment.ecb_booking_code,
            assignment.first_survey&.ecb_survey_code,
            assignment.first_survey&.old_survey_number,
            assignment.first_survey&.survey_partner&.name,
            assignment.first_survey&.summary_stage1,
            assignment.first_survey&.summary_stage2,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.last&.second&.capitalize&.gsub('_', ' '),
            assignment.first_installation&.ecb_install_code,
            assignment.charger_issued_date || assignment.first_installation&.charger_issued_date,
            assignment.first_installation&.charger_make_type,
            assignment.first_installation&.scheduled_install_date_time&.to_ist_format,
            assignment.first_installation&.install_completed_at&.to_ist_format,
            assignment.first_installation&.install_partner&.name,
            assignment.email,
            assignment.first_installation&.install_type_string,
            assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
            '',
            future_next_follow_up_at(assignment, assignment.first_survey, assignment.first_installation)&.to_ist_format,
            assignment.first_survey&.cable_length_new || assignment.first_survey&.cable_length,
            assignment.first_installation&.status_cancelled? ? '' : assignment.first_installation&.cable_length_new || assignment.first_installation&.cable_length,
            assignment.first_installation&.cable_gauge,
            assignment.first_installation&.install_completed_at && assignment.first_installation&.install_completed_at&.to_ist_format.eql?(assignment.first_survey&.survey_completed_at&.to_ist_format) ? 'Y' : 'N',
            '',
            '',
            '',
            '',
            if assignment.bhe
              'Y'
            else
              assignment.bhe.nil? ? '' : 'N'
            end,
            assignment.bhe_date&.to_ist_format,
            '',
            '',
            '',
            assignment.vin || assignment.first_installation&.vin,
            assignment.first_installation&.commission_number,
            '',
            '',
            assignment.first_installation&.sanctioned_load,
            assignment.first_installation&.avg_load_utilized || assignment.first_survey&.avg_load_utilized,
            assignment.first_survey&.customer_rating,
            assignment.first_installation&.customer_rating,
            assignment.uploaded_by&.name,
            assignment.first_installation&.type_of_power_cable,
            assignment.first_installation&.core_of_power_cable,
            assignment.first_installation&.mcb_used_on_site,
            assignment.first_installation&.mcb_box_used_on_site,
            if assignment.first_installation
              assignment.first_installation.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid'
            else
              ''
            end,
            if assignment.first_survey
              assignment.first_survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid'
            else
              ''
            end,
            assignment.first_survey&.is_no_show_during_survey ? 'Y' : 'N',
            assignment.first_installation&.is_no_show_during_installation ? 'Y' : 'N',
            assignment.first_installation&.is_extra_material_used ? 'Y' : 'N',
            assignment.first_survey&.surveyor_name,
            [assignment.first_installation&.first_contact_at, assignment.first_survey&.first_contact_at,
             assignment.first_contact_at].compact.min&.to_ist_format,
            assignment.cee_name,
            assignment.cee_contact_date,
            assignment.nature_of_work_string,
            assignment.expected_delivery_date,
            assignment.first_survey&.survey_readiness_information_date || assignment.survey_readiness_information_date,
            assignment.first_installation&.sow_status || assignment.first_survey&.sow_status,
            assignment.first_installation&.sow_completion_date || assignment.first_survey&.sow_completion_date,
            assignment.first_installation&.extra_cable_payment_status || assignment.first_survey&.extra_cable_payment_status,
            assignment.first_installation&.extra_cable_payment_date || assignment.first_survey&.extra_cable_payment_date,
            assignment.first_installation&.installation_readiness_information_date || assignment.first_survey&.installation_readiness_information_date,
            assignment.booking_status_updated_date,
            assignment.edd_status_updated_date,
            assignment.gst,
            assignment.dealer_gst,
            assignment.customer_proposed_survey_date,
            assignment.customer_proposed_survey_date_updated_at,
            assignment.customer_proposed_install_date,
            assignment.customer_proposed_install_date_updated_at,
            [assignment.first_installation&.updated_at, assignment.first_survey&.updated_at,
            assignment.updated_at].compact.max.to_ist_format
          ], types: column_types
        else
          assignment.surveys.each do |survey|
            if survey.installations.size == 0
              counter += 1

              sheet.add_row [
                counter,
                assignment.brand.name,
                assignment.zone&.name,
                assignment.assigned_city_manager&.name,
                assignment.dealer_name,
                assignment.dealer_location,
                assignment.dealer_email,
                assignment.city_manager_email,
                assignment.zonal_manager_email,
                assignment.customer_name,
                assignment.brand_booking_code,
                assignment.brand_booking_date&.to_ist_format,
                assignment.brand_booking_status,
                survey.status_text,
                '',
                assignment.status_text,
                assignment.brand_model&.name,
                survey.model_variant || assignment.model_variant,
                assignment.model_colour,
                survey.obc || assignment.obc,
                survey.address_line1,
                survey.address_line2,
                survey.customer_city,
                assignment.city&.name,
                survey.pincode,
                assignment.state&.name,
                assignment.contact_number,
                assignment.alt_contact_number,
                assignment.ecb_receive_date&.to_ist_format,
                survey.booking&.created_at&.to_ist_format,
                survey&.created_at&.to_ist_format,
                '',
                survey&.approved_at&.to_ist_format,
                '',
                (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks).sort.reverse.map(&:last).join(', '),
                if assignment.is_outstation
                  'Y'
                else
                  assignment.is_outstation.nil? ? '' : 'N'
                end,
                if survey.is_outstation
                  'Y'
                else
                  survey.is_outstation.nil? ? '' : 'N'
                end,
                '',
                if assignment.is_outstation_for_client
                  'Y'
                else
                  assignment.is_outstation_for_client.nil? ? '' : 'N'
                end,
                if survey.is_outstation_for_client
                  'Y'
                else
                  survey.is_outstation_for_client.nil? ? '' : 'N'
                end,
                '',
                survey.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
                assignment.customer_ok_with_recording ? 'Y' : 'N',
                survey.scheduled_survey_date_time&.to_ist_format,
                survey.survey_completed_at&.to_ist_format,
                assignment.ecb_booking_code,
                survey.ecb_survey_code,
                survey.old_survey_number,
                survey.survey_partner&.name,
                survey.summary_stage1,
                survey.summary_stage2,
                (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks).sort.last&.second&.capitalize&.gsub('_', ' '),
                '',
                assignment.charger_issued_date,
                '',
                '',
                '',
                '',
                assignment.email,
                '',
                '',
                '',
                future_next_follow_up_at(assignment, survey, nil)&.to_ist_format,
                survey.cable_length_new || survey.cable_length,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                if assignment.bhe
                  'Y'
                else
                  assignment.bhe.nil? ? '' : 'N'
                end,
                assignment.bhe_date&.to_ist_format,
                '',
                '',
                '',
                assignment.vin || survey.first_installation&.vin,
                '',
                '',
                '',
                survey.avg_load_utilized,
                '',
                survey&.customer_rating,
                '',
                assignment.uploaded_by&.name,
                survey.type_of_power_cable,
                survey.core_of_power_cable,
                survey.mcb_used_on_site,
                survey.mcb_box_used_on_site,
                survey.first_installation&.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                survey.is_no_show_during_survey ? 'Y' : 'N',
                '',
                '',
                survey.surveyor_name,
                [assignment.first_contact_at, survey.first_contact_at].compact.min&.to_ist_format,
                assignment.cee_name,
                assignment.cee_contact_date,
                survey.nature_of_work_string,
                assignment.expected_delivery_date,
                survey.survey_readiness_information_date || assignment.survey_readiness_information_date,
                survey.first_installation&.sow_status || survey.sow_status,
                survey.first_installation&.sow_completion_date || survey.sow_completion_date,
                survey.first_installation&.extra_cable_payment_status || survey.extra_cable_payment_status,
                survey.first_installation&.extra_cable_payment_date || survey.extra_cable_payment_date,
                survey.first_installation&.installation_readiness_information_date || survey.installation_readiness_information_date,
                assignment.booking_status_updated_date,
                assignment.edd_status_updated_date,
                assignment.gst,
                assignment.dealer_gst,
                assignment.customer_proposed_survey_date,
                assignment.customer_proposed_survey_date_updated_at,
                assignment.customer_proposed_install_date,
                assignment.customer_proposed_install_date_updated_at,
                [assignment.updated_at, survey.updated_at].compact.max.to_ist_format
              ], types: column_types
            else
              survey.installations.each do |installation|
                counter += 1

                sheet.add_row [
                  counter,
                  assignment.brand.name,
                  assignment.zone&.name,
                  assignment.assigned_city_manager&.name,
                  assignment.dealer_name,
                  assignment.dealer_location,
                  assignment.dealer_email,
                  assignment.city_manager_email,
                  assignment.zonal_manager_email,
                  assignment.customer_name,
                  assignment.brand_booking_code,
                  assignment.brand_booking_date&.to_ist_format,
                  assignment.brand_booking_status,
                  survey.status_text,
                  installation.status_text,
                  assignment.status_text,
                  assignment.brand_model&.name,
                  survey.model_variant || assignment.model_variant,
                  assignment.model_colour,
                  survey.obc || assignment.obc,
                  survey.address_line1,
                  survey.address_line2,
                  installation.customer_city,
                  assignment.city&.name,
                  survey.pincode,
                  assignment.state&.name,
                  assignment.contact_number,
                  assignment.alt_contact_number,
                  assignment.ecb_receive_date&.to_ist_format,
                  assignment.created_at&.to_ist_format,
                  installation.survey&.created_at&.to_ist_format,
                  installation&.created_at&.to_ist_format,
                  installation&.survey&.approved_at&.to_ist_format,
                  installation&.approved_at&.to_ist_format,
                  (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks).sort.reverse.map(&:last).join(', '),
                  if assignment.is_outstation
                    'Y'
                  else
                    assignment.is_outstation.nil? ? '' : 'N'
                  end,
                  if survey.is_outstation
                    'Y'
                  else
                    survey.is_outstation.nil? ? '' : 'N'
                  end,
                  if installation.is_outstation
                    'Y'
                  else
                    installation.is_outstation.nil? ? '' : 'N'
                  end,
                  if assignment.is_outstation_for_client
                    'Y'
                  else
                    assignment.is_outstation_for_client.nil? ? '' : 'N'
                  end,
                  if survey.is_outstation_for_client
                    'Y'
                  else
                    survey.is_outstation_for_client.nil? ? '' : 'N'
                  end,
                  if installation.is_outstation_for_client
                    'Y'
                  else
                    installation.is_outstation_for_client.nil? ? '' : 'N'
                  end,
                  survey.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
                  assignment.customer_ok_with_recording ? 'Y' : 'N',
                  survey.scheduled_survey_date_time&.to_ist_format,
                  survey.survey_completed_at&.to_ist_format,
                  assignment.ecb_booking_code,
                  survey.ecb_survey_code,
                  survey.old_survey_number,
                  survey.survey_partner&.name,
                  survey.summary_stage1,
                  survey.summary_stage2,
                  (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks).sort.last&.second&.capitalize&.gsub('_', ' '),
                  installation.ecb_install_code,
                  assignment.charger_issued_date || installation.charger_issued_date,
                  installation.charger_make_type,
                  installation.scheduled_install_date_time&.to_ist_format,
                  installation.install_completed_at&.to_ist_format,
                  installation.install_partner&.name,
                  assignment.email,
                  installation.install_type_string,
                  assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
                  '',
                  future_next_follow_up_at(assignment, survey, installation)&.to_ist_format,
                  survey.cable_length_new || survey.cable_length,
                  installation.status_cancelled? ? '' : installation.cable_length_new || installation.cable_length,
                  installation.cable_gauge,
                  installation.install_completed_at && installation.install_completed_at.to_ist_format.eql?(survey.survey_completed_at&.to_ist_format) ? 'Y' : 'N',
                  '',
                  '',
                  '',
                  '',
                  if assignment.bhe
                    'Y'
                  else
                    assignment.bhe.nil? ? '' : 'N'
                  end,
                  assignment.bhe_date&.to_ist_format,
                  '',
                  '',
                  '',
                  assignment.vin || installation.vin,
                  installation.commission_number,
                  '',
                  '',
                  installation.sanctioned_load,
                  installation.avg_load_utilized,
                  '',
                  installation&.customer_rating,
                  assignment.uploaded_by&.name,
                  installation.type_of_power_cable,
                  installation.core_of_power_cable,
                  installation.mcb_used_on_site,
                  installation.mcb_box_used_on_site,
                  installation.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                  survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                  survey.is_no_show_during_survey ? 'Y' : 'N',
                  installation.is_no_show_during_installation ? 'Y' : 'N',
                  installation.is_extra_material_used ? 'Y' : 'N',
                  survey.surveyor_name,
                  [assignment.first_contact_at, survey.first_contact_at,
                   installation.first_contact_at].compact.min&.to_ist_format,
                  assignment.cee_name,
                  assignment.cee_contact_date,
                  installation.nature_of_work_string,
                  assignment.expected_delivery_date,
                  survey.survey_readiness_information_date || assignment.survey_readiness_information_date,
                  installation.sow_status || survey.sow_status,
                  installation.sow_completion_date || survey.sow_completion_date,
                  installation.extra_cable_payment_status || survey.extra_cable_payment_status,
                  installation.extra_cable_payment_date || survey.extra_cable_payment_date,
                  installation.installation_readiness_information_date || survey.installation_readiness_information_date,
                  assignment.booking_status_updated_date,
                  assignment.edd_status_updated_date,
                  assignment.gst,
                  assignment.dealer_gst,
                  assignment.customer_proposed_survey_date,
                  assignment.customer_proposed_survey_date_updated_at,
                  assignment.customer_proposed_install_date,
                  assignment.customer_proposed_install_date_updated_at,
                  [assignment.updated_at, survey.updated_at, installation.updated_at].compact.max.to_ist_format
                ], types: column_types
              end
            end
          end
        end
      end

      counter
    end

    def future_next_follow_up_at(booking, survey, installation)
      follow_ups = [booking&.next_follow_up_at, survey&.next_follow_up_at, installation&.next_follow_up_at].compact
      future_follow_ups = follow_ups.select { |follow_up| follow_up > time_now }
      future_follow_ups.min
    end

    def time_now
      @time_now ||= Time.now
    end
  end
end
