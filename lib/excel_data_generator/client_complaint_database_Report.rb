module ExcelDataGenerator
  class ClientComplaintDatabaseReport
    def initialize(complaints, xlsx_package, skip_header_row = false, index = 0)
      @complaints = complaints
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          "SN",
          "Brand Name",
          "ZONE",
          "Dealer Name",
          "Dealer Location",
          "Customer Name",
          "complaint Ref No",
          "Visit Ref No",
          "Complaint Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Visit Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Complaint closure date",
          "Complaint closure remarks",
          "Model Name",
          "Charger Type",
          "Customer Address 1",
          "Customer Address2",
          "Customer city",
          "PIN Code",
          "State",
          "Customer Phone Number1",
          "Customer Phone Number2",
          "Complaint rcv date",
          "Visit remarks",
          "Customer Contact Logs",
          "Visit Completion date",
          "Make of Charger",
          "Installation end Date",
          "Charger Serial No.",
          "VIN"
        ]
      end

      # Create entries for each complaint
      counter = @index
      @complaints.each do |complaint|
        if complaint.visits.count <= 1
          counter = counter + 1
          sheet.add_row [
            counter,
            complaint.brand.name,
            complaint.zone&.name,
            complaint.dealer_name,
            complaint.dealer_location,
            complaint.customer_name,
            complaint.ecb_complaint_code,
            complaint.first_visit&.ecb_visit_code,
            complaint.status,
            complaint.first_visit&.status,
            complaint.complaint_closed_at,
            complaint.close_reason,
            complaint.first_visit&.complaint_brand_model_name,
            complaint.obc,
            complaint.first_visit&.address_line1,
            complaint.first_visit&.address_line2,
            complaint.first_visit&.customer_city,
            complaint.pincode,
            complaint.state&.name,
            complaint.contact_number,
            complaint.alt_contact_number,
            complaint.complaint_receive_date&.to_ist_format,
            complaint.first_visit&.visit_remarks,
            (complaint.customer_connect_logs_remarks + (complaint.first_visit ? complaint.first_visit.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(", "),
            complaint.first_visit&.visit_completed_at&.to_ist_format,
            complaint.first_visit&.charger_make_type,
            complaint.first_visit&.survey&.first_installation&.install_completed_at&.to_ist_format,
            complaint.first_visit&.charger_serial_number ? " #{complaint.first_visit&.charger_serial_number}" : '',
            complaint.first_visit&.vin
          ], types: column_types
        else
          complaint.visits.each do |visit|
            counter = counter + 1
            sheet.add_row [
              counter,
              complaint.brand.name,
              complaint.zone&.name,
              complaint.dealer_name,
              complaint.dealer_location,
              complaint.customer_name,
              complaint.ecb_complaint_code,
              visit.ecb_visit_code,
              complaint.status,
              visit.status,
              complaint.complaint_closed_at,
              complaint.close_reason,
              visit.complaint_brand_model_name,
              complaint.obc,
              visit&.address_line1,
              visit&.address_line2,
              visit&.customer_city,
              complaint.pincode,
              complaint.state&.name,
              complaint.contact_number,
              complaint.alt_contact_number,
              complaint.complaint_receive_date&.to_ist_format,
              visit.visit_remarks,
              (complaint.customer_connect_logs_remarks + (visit ? visit.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(", "),
              visit&.visit_completed_at&.to_ist_format,
              visit&.charger_make_type,
              visit&.survey&.first_installation&.install_completed_at&.to_ist_format,
              visit&.charger_serial_number ? " #{visit&.charger_serial_number}" : '',
              visit&.vin
            ], types: column_types
          end
        end
      end

      counter
    end
  end
end
