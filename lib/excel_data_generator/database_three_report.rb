module ExcelDataGenerator
  class DatabaseThreeReport
    def initialize(assignments, xlsx_package, skip_header_row = false, index = 0)
      @assignments = assignments
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          "SN",
          "Brand Name",
          "ZONE",
          "City Manager",
          "Dealer Name",
          "Dealer Location",
          "Dealer Email ID",
          "City Manager Email ID",
          "Zonal Manager Email ID",
          "Customer Name",
          "Customer Booking Ref No",
          "Booking Date",
          "Model Name",
          "Variant",
          "Colour",
          "Charger Type",
          "Customer Address 1",
          "Customer Address2",
          "Customer city of Survey/Installation",
          "EV city",
          "PIN Code",
          "State",
          "Customer Phone Number1",
          "Customer Phone Number2",
          "ECB Booking rcv date",
          "Customer Contact Logs",
          "Survey Outstation for Installer Y/N",
          "Confirmed for Survey (Y/N)",
          "Scheduled Date of survey",
          "Survey end Date",
          "Survey Ref Number",
          "Old Survey Number",
          "Survey Partner Name",
          "Broad Survey Findings Stage 1",
          "Broad Survey Findings Stage 2",
          "Comments",
          "Installation Ref Number",
          "Date of issue of charger by the dealer to the customer",
          "Make of Charger",
          "Scheduled Date of Installation",
          "Installation end Date",
          "Installer Partner Name ",
          "Customer Email ID",
          "Installation Type",
          "Charger Serial No.",
          "Attributes",
          "Next Follow up date",
          "cable length at survey",
          "cable length at installation",
          "Gauge of cable sq mm [2.5 / 4 / 6 / 10 /16 ] at installation",
          "Survey and installation on same day [Y/N]",
          "No show date [ at survey stage ]",
          "No show comments",
          "No show date [installation stage]",
          "No show comments",
          "BHE",
          "BHE Date",
          "Installation addresss [if different from the customer address mentioned in Column L]",
          "Extra Switch gear/Material used at site provided by installer after ECB approval?",
          "Final Status",
          "VIN ",
          "Type of power cable",
          "Core of power cable",
          "MCB used on site",
          "MCB box used on site",
          "Is this client (OEM) paid - Installation ?",
          "Is this client (OEM) paid - Survey ?",
          "CEE Name",
          "CEE Contact Date",
          "Expected Delivery Date",
          "Last Updated"
        ]
      end

      # Create entries for each assignment
      counter = @index
      @assignments.each do | assignment |
        if assignment.surveys.count <= 1 && assignment.installations.count <= 1
          counter = counter + 1
          sheet.add_row [
            counter,
            assignment.brand.name,
            assignment.zone&.name,
            assignment.assigned_city_manager&.name,
            assignment.dealer_name,
            assignment.dealer_location,
            assignment.dealer_email,
            assignment.city_manager_email,
            assignment.zonal_manager_email,
            assignment.customer_name,
            assignment.brand_booking_code,
            assignment.brand_booking_date&.to_ist_format,
            assignment.brand_model&.name,
            assignment.model_variant,
            assignment.model_colour,
            assignment.obc,
            assignment.first_survey&.address_line1,
            assignment.first_survey&.address_line2,
            assignment.first_survey&.customer_city,
            assignment.city&.name,
            assignment.first_survey&.pincode || assignment.pincode,
            assignment.state&.name,
            assignment.contact_number,
            assignment.alt_contact_number,
            assignment.ecb_receive_date&.to_ist_format,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(", "),
            assignment.first_survey&.is_outstation ? 'Y' : assignment.first_survey&.is_outstation.nil? ? '' : 'N',
            assignment.first_survey&.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
            assignment.first_survey&.scheduled_survey_date_time&.to_ist_format,
            assignment.first_survey&.survey_completed_at&.to_ist_format,
            assignment.first_survey&.ecb_survey_code,
            assignment.first_survey&.old_survey_number,
            assignment.first_survey&.survey_partner&.name,
            assignment.first_survey&.summary_stage1,
            assignment.first_survey&.summary_stage2,
            (assignment.customer_connect_logs_remarks + (assignment.first_survey ? assignment.first_survey.customer_connect_logs_remarks : []) + (assignment.first_installation ? assignment.first_installation.customer_connect_logs_remarks : [])).sort.last&.second&.capitalize&.gsub('_', ' '),
            assignment.first_installation&.ecb_install_code,
            assignment.charger_issued_date || assignment.first_installation&.charger_issued_date,
            assignment.first_installation&.charger_make_type,
            assignment.first_installation&.scheduled_install_date_time&.to_ist_format,
            assignment.first_installation&.install_completed_at&.to_ist_format,
            assignment.first_installation&.install_partner&.name,
            assignment.email,
            assignment.first_installation&.install_type_string,
            assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
            '',
            future_next_follow_up_at(assignment, assignment.first_survey, assignment.first_installation)&.to_ist_format,
            assignment.first_survey&.cable_length_new || assignment.first_survey&.cable_length,
            assignment.first_installation&.status_cancelled? ? '' : assignment.first_installation&.cable_length_new || assignment.first_installation&.cable_length,
            assignment.first_installation&.cable_gauge,
            assignment.first_installation&.install_completed_at && assignment.first_installation&.install_completed_at.to_ist_format.eql?(assignment.first_survey&.survey_completed_at&.to_ist_format) ? 'Y' : 'N',
            '',
            '',
            '',
            '',
            assignment.bhe ? 'Y' : assignment.bhe.nil? ? '' : 'N',
            assignment.bhe_date&.to_ist_format,
            '',
            '',
            '',
            assignment.first_installation&.vin,
            assignment.first_installation&.type_of_power_cable,
            assignment.first_installation&.core_of_power_cable,
            assignment.first_installation&.mcb_used_on_site,
            assignment.first_installation&.mcb_box_used_on_site,
            assignment.first_installation ? (assignment.first_installation.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid') : '',
            assignment.first_survey ? (assignment.first_survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid') : '',
            assignment.cee_name,
            assignment.cee_contact_date,
            assignment.expected_delivery_date,
            [assignment.first_installation&.updated_at, assignment.first_survey&.updated_at, assignment.updated_at].compact.max.to_ist_format
          ], types: column_types
        else
          assignment.surveys.each do | survey |
            if survey.installations.count == 0
              counter = counter + 1
              sheet.add_row [
                counter,
                assignment.brand.name,
                assignment.zone&.name,
                assignment.assigned_city_manager&.name,
                assignment.dealer_name,
                assignment.dealer_location,
                assignment.dealer_email,
                assignment.city_manager_email,
                assignment.zonal_manager_email,
                assignment.customer_name,
                assignment.brand_booking_code,
                assignment.brand_booking_date&.to_ist_format,
                assignment.brand_model&.name,
                assignment.model_variant,
                assignment.model_colour,
                assignment.obc,
                survey.address_line1,
                survey.address_line2,
                survey.customer_city,
                assignment.city&.name,
                survey.pincode,
                assignment.state&.name,
                assignment.contact_number,
                assignment.alt_contact_number,
                assignment.ecb_receive_date&.to_ist_format,
                (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks ).sort.reverse.map(&:last).join(", "),
                survey.is_outstation ? 'Y' : survey.is_outstation.nil? ? '' : 'N',
                survey.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
                survey.scheduled_survey_date_time&.to_ist_format,
                survey.survey_completed_at&.to_ist_format,
                survey.ecb_survey_code,
                survey.old_survey_number,
                survey.survey_partner&.name,
                survey.summary_stage1,
                survey.summary_stage2,
                (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks ).sort.last&.second&.capitalize&.gsub('_', ' '),
                '',
                assignment.charger_issued_date,
                '',
                '',
                '',
                '',
                assignment.email,
                '',
                '',
                '',
                future_next_follow_up_at(assignment, survey, nil)&.to_ist_format,
                survey.cable_length_new || survey.cable_length,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                assignment.bhe ? 'Y' : assignment.bhe.nil? ? '' : 'N',
                assignment.bhe_date&.to_ist_format,
                '',
                '',
                '',
                '',
                survey.type_of_power_cable,
                survey.core_of_power_cable,
                survey.mcb_used_on_site,
                survey.mcb_box_used_on_site,
                survey.first_installation&.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                survey.customer_billing ? 'Client/OEM paid' : 'Customer/Dealer paid',
                assignment.cee_name,
                assignment.cee_contact_date,
                assignment.expected_delivery_date,
                [survey.updated_at, assignment.updated_at].compact.max.to_ist_format
              ], types: column_types
            else
              survey.installations.each do | installation |
                counter = counter + 1
                sheet.add_row [
                  counter,
                  assignment.brand.name,
                  assignment.zone&.name,
                  assignment.assigned_city_manager&.name,
                  assignment.dealer_name,
                  assignment.dealer_location,
                  assignment.dealer_email,
                  assignment.city_manager_email,
                  assignment.zonal_manager_email,
                  assignment.customer_name,
                  assignment.brand_booking_code,
                  assignment.brand_booking_date&.to_ist_format,
                  assignment.brand_model&.name,
                  assignment.model_variant,
                  assignment.model_colour,
                  assignment.obc,
                  survey.address_line1,
                  survey.address_line2,
                  installation.customer_city,
                  assignment.city&.name,
                  survey.pincode,
                  assignment.state&.name,
                  assignment.contact_number,
                  assignment.alt_contact_number,
                  assignment.ecb_receive_date&.to_ist_format,
                  (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks ).sort.reverse.map(&:last).join(', '),
                  survey.is_outstation ? 'Y' : survey.is_outstation.nil? ? '' : 'N',
                  survey.customer_survey_confirmed || assignment.customer_survey_confirmed ? 'Y' : 'N',
                  survey.scheduled_survey_date_time&.to_ist_format,
                  survey.survey_completed_at&.to_ist_format,
                  survey.ecb_survey_code,
                  survey.old_survey_number,
                  survey.survey_partner&.name,
                  survey.summary_stage1,
                  survey.summary_stage2,
                  (assignment.customer_connect_logs_remarks + survey.customer_connect_logs_remarks + installation.customer_connect_logs_remarks ).sort.last&.second&.capitalize&.gsub('_', ' '),
                  installation.ecb_install_code,
                  assignment.charger_issued_date || installation.charger_issued_date,
                  installation.charger_make_type,
                  installation.scheduled_install_date_time&.to_ist_format,
                  installation.install_completed_at&.to_ist_format,
                  installation.install_partner&.name,
                  assignment.email,
                  installation.install_type_string,
                  assignment.first_installation&.charger_serial_number ? " #{assignment.first_installation&.charger_serial_number}" : '',
                  '',
                  future_next_follow_up_at(assignment, survey, installation)&.to_ist_format,
                  survey.cable_length_new || survey.cable_length,
                  installation.status_cancelled? ? '' : installation.cable_length_new || installation.cable_length,
                  installation.cable_gauge,
                  installation.install_completed_at && installation.install_completed_at.to_ist_format.eql?(survey.survey_completed_at&.to_ist_format) ? 'Y' : 'N',
                  '',
                  '',
                  '',
                  '',
                  assignment.bhe ? 'Y' : assignment.bhe.nil? ? '' : 'N',
                  assignment.bhe_date&.to_ist_format,
                  '',
                  '',
                  '',
                  installation.vin,
                  installation.type_of_power_cable,
                  installation.core_of_power_cable,
                  installation.mcb_used_on_site,
                  installation.mcb_box_used_on_site,
                  installation.customer_billing  ? 'Client/OEM paid' : 'Customer/Dealer paid',
                  survey.customer_billing  ? 'Client/OEM paid' : 'Customer/Dealer paid',
                  assignment.cee_name,
                  assignment.cee_contact_date,
                  assignment.expected_delivery_date,
                  [installation.updated_at, survey.updated_at, assignment.updated_at].compact.max.to_ist_format
                ], types: column_types
              end
            end
          end
        end
      end

      counter
    end

    def future_next_follow_up_at(booking, survey, installation)
      follow_ups = [booking&.next_follow_up_at, survey&.next_follow_up_at, installation&.next_follow_up_at].compact
      future_follow_ups = follow_ups.select{|follow_up| follow_up > time_now}
      future_follow_ups.min
    end

    def time_now
      @time_now ||= Time.now
    end
  end
end
