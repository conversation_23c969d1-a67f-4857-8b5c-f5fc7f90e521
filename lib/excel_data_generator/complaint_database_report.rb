module ExcelDataGenerator
  class ComplaintDatabaseReport
    def initialize(complaints, xlsx_package, skip_header_row = false, index = 0)
      @complaints = complaints
      @xlsx_package = xlsx_package
      @skip_header_row = skip_header_row
      @index = index
    end

    def generate
      wb = @xlsx_package.workbook

      sheet = @index.zero? ? wb.add_worksheet(name: "Data") : wb.sheet_by_name("Data")

      column_types = Array.new(81)
      # column_types[51]= :string

      unless @skip_header_row
        # Create the header row
        sheet.add_row [
          "SN",
          "Brand Name",
          "ZON<PERSON>",
          "City Manager",
          "Dealer Name",
          "Dealer Location",
          "Customer Name",
          "complaint Ref No",
          "Visit Ref No",
          "Complaint Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Visit Status as on #{Date.today.strftime('%d-%m-%Y')}",
          "Complaint closure date",
          "Complaint closure remarks",
          "Model Name",
          "Variant",
          "Colour",
          "Charger Type",
          "Customer Address 1",
          "Customer Address2",
          "Customer city",
          "EV city",
          "PIN Code",
          "State",
          "Customer Phone Number1",
          "Customer Phone Number2",
          "Complaint rcv date",
          "Complaint Creation Date",
          "Visit Creation Date",
          "Visit remarks",
          "Customer Contact Logs",
          "Scheduled Date of visit",
          "Visit Completion date",
          "Survey Ref Number",
          "Survey Date",
          "Visit Partner Name",
          "Broad Visit Findings Stage 1",
          "Survey Partner Name",
          "Installation Ref Number",
          "Make of Charger",
          "Installation end Date",
          "Installer Partner Name",
          "Charger Serial No.",
          "Next Follow up date",
          "VIN",
          "Visit Rating",
          "Complaint Uploaded By",
          "Other Reasons",
          "Installation Outstation for Installer Y/N",
          "Type of power cable",
          "Core of power cable",
          "Size of power cable used on site (in sq mm) --(2.5 sqmm, 4 sqmm, 6 sqmm, 10 sqmm, 16 sqmm or any other))",
          "Length of power cable used (in metres)",
          "Last Updated"
        ]
      end

      # Create entries for each complaint
      counter = @index
      @complaints.each do |complaint|
        if complaint.visits.count <= 1
          counter = counter + 1
          sheet.add_row [
            counter,
            complaint.brand.name,
            complaint.zone&.name,
            complaint.assigned_city_manager&.name,
            complaint.dealer_name,
            complaint.dealer_location,
            complaint.customer_name,
            complaint.ecb_complaint_code,
            complaint.first_visit&.ecb_visit_code,
            complaint.status,
            complaint.first_visit&.status,
            complaint.complaint_closed_at,
            complaint.close_reason,
            complaint.first_visit&.complaint_brand_model_name,
            complaint.model_variant,
            complaint.model_colour,
            complaint.obc,
            complaint.first_visit&.address_line1,
            complaint.first_visit&.address_line2,
            complaint.first_visit&.customer_city,
            complaint.city&.name,
            complaint.pincode,
            complaint.state&.name,
            complaint.contact_number,
            complaint.alt_contact_number,
            complaint.complaint_receive_date&.to_ist_format,
            complaint&.created_at&.to_ist_format,
            complaint.first_visit&.created_at&.to_ist_format,
            complaint.first_visit&.visit_remarks,
            (complaint.customer_connect_logs_remarks + (complaint.first_visit ? complaint.first_visit.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(", "),
            complaint.first_visit&.scheduled_visit_date_time&.to_ist_format,
            complaint.first_visit&.visit_completed_at&.to_ist_format,
            complaint.first_visit&.ecb_survey_code,
            complaint.first_visit&.survey&.survey_completed_at&.to_ist_format,
            complaint.first_visit&.visit_partner&.name,
            complaint.first_visit&.summary_stage1,
            complaint.first_visit&.survey&.survey_partner&.name,
            complaint.first_visit&.ecb_install_code,
            complaint.first_visit&.charger_make_type,
            complaint.first_visit&.survey&.first_installation&.install_completed_at&.to_ist_format,
            complaint.first_visit&.survey&.first_installation&.install_partner&.name,
            complaint.first_visit&.charger_serial_number ? " #{complaint.first_visit&.charger_serial_number}" : '',
            complaint.next_follow_up_at&.to_ist_format,
            complaint.first_visit&.vin,
            complaint.first_visit&.customer_rating,
            complaint.uploaded_by&.name,
            complaint.close_reason == "OthersReasons" ? complaint.close_complaint_summary  : " ",
            complaint.is_outstation ? 'Y' : complaint.is_outstation.nil? ? '' : 'N',
            complaint.first_visit&.type_of_power_cable,
            complaint.first_visit&.core_of_power_cable,
            complaint.first_visit&.cable_gauge,
            complaint.first_visit&.cable_length_new,
            complaint.first_visit&.updated_at&.to_ist_format
          ], types: column_types
        else
          complaint.visits.each do |visit|
            counter = counter + 1
            sheet.add_row [
              counter,
              complaint.brand.name,
              complaint.zone&.name,
              complaint.assigned_city_manager&.name,
              complaint.dealer_name,
              complaint.dealer_location,
              complaint.customer_name,
              complaint.ecb_complaint_code,
              visit.ecb_visit_code,
              complaint.status,
              visit.status,
              complaint.complaint_closed_at,
              complaint.close_reason,
              visit.complaint_brand_model_name,
              complaint.model_variant,
              complaint.model_colour,
              complaint.obc,
              visit&.address_line1,
              visit&.address_line2,
              visit&.customer_city,
              complaint.city&.name,
              complaint.pincode,
              complaint.state&.name,
              complaint.contact_number,
              complaint.alt_contact_number,
              complaint.complaint_receive_date&.to_ist_format,
              complaint&.created_at&.to_ist_format,
              visit&.created_at&.to_ist_format,
              visit.visit_remarks,
              (complaint.customer_connect_logs_remarks + (visit ? visit.customer_connect_logs_remarks : [])).sort.reverse.map(&:last).join(", "),
              visit&.scheduled_visit_date_time&.to_ist_format,
              visit&.visit_completed_at&.to_ist_format,
              visit&.ecb_survey_code,
              visit&.survey&.survey_completed_at&.to_ist_format,
              visit&.visit_partner&.name,
              visit&.summary_stage1,
              visit&.survey&.survey_partner&.name,
              visit&.ecb_install_code,
              visit&.charger_make_type,
              visit&.survey&.first_installation&.install_completed_at&.to_ist_format,
              visit&.survey&.first_installation&.install_partner&.name,
              visit&.charger_serial_number ? " #{visit&.charger_serial_number}" : '',
              complaint.next_follow_up_at&.to_ist_format,
              visit&.vin,
              visit&.customer_rating,
              complaint.uploaded_by&.name,
              complaint.close_reason == "OthersReasons" ? complaint.close_complaint_summary  : " ",
              visit.is_outstation ? 'Y' : visit.is_outstation.nil? ? '' : 'N',
              visit.type_of_power_cable,
              visit.core_of_power_cable,
              visit.cable_gauge,
              visit.cable_length_new,
              visit&.updated_at&.to_ist_format
            ], types: column_types
          end
        end
      end

      counter
    end
  end
end
