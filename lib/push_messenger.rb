module PushMessenger
  class Gcm
    def deliver(tokens, payload, _expiry = 1.day.to_i)
      # tokens = *tokens
      # n = Rpush::Gcm::Notification.new
      # n.app = Rpush::Gcm::App.find_by_name('android_app')
      # n.registration_ids = tokens
      # n.expiry = expiry
      # n.data = payload
      # n.priority = 'high'
      # # n.content_available = true # Optional
      # # Optional notification payload. See the reference below for more keys you can use!
      # # n.notification = { body: 'great match!',
      # #                    title: 'Portugal vs. Denmark',
      # #                    icon: 'myicon'
      # #                  }
      # n.save!
      # Rails.logger.info("Rpush data: #{tokens}, payload: #{payload}")
      # NotificationJob.perform_later
      FcmV1NotificationJob.perform_later(tokens, payload)
    end
  end
end
