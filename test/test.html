<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Testing File Uploads</title>
    <link
            href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css"
            rel="stylesheet"
    />
</head>
<body>
<section
        class="max-w-4xl mt-4 w-11/12 mx-auto bg-blue-50 p-8 shadow-lg rounded-lg"
>
    <h1 class="text-3xl font-semibold text-center">Add a Brand</h1>
    <form>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-3" for="name">
                Name
            </label>
            <input
                    type="text"
                    name="name"
                    id="name"
                    class="w-full block p-3 shadow-sm"
                    placeholder="Name"
            />
        </fieldset>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-3" for="code">
                Code
            </label>
            <input
                    type="text"
                    name="code"
                    id="code"
                    class="w-full block p-3 shadow-sm"
                    placeholder="Code"
            />
        </fieldset>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-0" for="brand_logo">
                Brand Logo
            </label>
            <input
                    type="file"
                    name="brand_logo"
                    id="brand_logo"
                    class="w-full block p-3"
                    placeholder="Brand Logo"
            />
        </fieldset>
        <button
                class="w-full block p-3 bg-green-400 hover:bg-green-500 text-center transition duration-200"
                type="submit"
        >
            Create Brand
        </button>
    </form>
</section>
<script type="text/javascript">
  document.addEventListener('DOMContentLoaded', () => {
    document.addEventListener('submit', (e) => {
      e.preventDefault()
      const form = e.target
      var myHeaders = new Headers();
        myHeaders.append("access-token", "iO-9V22Clciv_RWlLY1Tpw");
        myHeaders.append("token-type", "Bearer");
        myHeaders.append("client", "-m33CptwWwONZnZa4OgzAA");
        myHeaders.append("uid", "<EMAIL>");
      const formData = new FormData()
      formData.append('brand[name]', form.name.value)
      formData.append('brand[code]', form.code.value)
      formData.append('brand[brand_logo]', form.brand_logo.files[0], form.brand_logo.value)
      var requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: formData,
          redirect: 'follow'
        };
      fetch("http://localhost:3000/api/v1/brands", requestOptions)
  .then(response => response.text())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
    })
  })
</script>
</body>
</html>