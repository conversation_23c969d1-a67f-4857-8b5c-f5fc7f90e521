<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Testing File Uploads</title>
    <link
            href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css"
            rel="stylesheet"
    />
</head>
<body>
<section
        class="max-w-4xl mt-4 w-11/12 mx-auto bg-blue-50 p-8 shadow-lg rounded-lg"
>
    <h1 class="text-3xl font-semibold text-center">Submit form response</h1>
    <form>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-3" for="form_section_id">
                form_section_id
            </label>
            <input
                    type="text"
                    name="form_section_id"
                    id="form_section_id"
                    class="w-full block p-3 shadow-sm"
                    placeholder="form_section_id"
            />
        </fieldset>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-3" for="form_question_id">
                form_question_id
            </label>
            <input
                    type="text"
                    name="form_question_id"
                    id="form_question_id"
                    class="w-full block p-3 shadow-sm"
                    placeholder="form_question_id"
            />
        </fieldset>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-3" for="response">
                response
            </label>
            <input
                    type="text"
                    name="response"
                    id="response"
                    class="w-full block p-3 shadow-sm"
                    placeholder="response"
            />
        </fieldset>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-0" for="images">
                Images
            </label>
            <input
                    type="file"
                    name="images[]" multiple="true"
                    id="images"
                    class="w-full block p-3"
                    placeholder="images"
            />
        </fieldset>
        <fieldset class="my-2">
            <label class="w-full block uppercase ml-3 mb-0" for="videos">
                Videos
            </label>
            <input
                    type="file"
                    name="videos[]" multiple="true"
                    id="videos"
                    class="w-full block p-3"
                    placeholder="videos"
            />
        </fieldset>
        <button
                class="w-full block p-3 bg-green-400 hover:bg-green-500 text-center transition duration-200"
                type="submit"
        >
            Submit Form response
        </button>
    </form>
</section>
<script type="text/javascript">
  document.addEventListener('DOMContentLoaded', () => {
    document.addEventListener('submit', (e) => {
      e.preventDefault()
      const form = e.target
      var myHeaders = new Headers();
        myHeaders.append("access-token", "LVd6h0Obc7s9au8-0KuLKw");
        myHeaders.append("token-type", "Bearer");
        myHeaders.append("client", "zd7QTraNrZhLrkD7vW_87g");
        myHeaders.append("uid", "<EMAIL>");
      const formData = new FormData()
      formData.append('response[form_section_id]', form.form_section_id.value)
      formData.append('response[form_question_id]', form.form_question_id.value)
      formData.append('response[response]', form.response.value)
      var files = document.getElementById('images').files.length;
        for (var x = 0; x < files; x++) {
            formData.append("response[images][]", document.getElementById('images').files[x]);
        }
        var files = document.getElementById('videos').files.length;
        for (var x = 0; x < files; x++) {
            formData.append("response[videos][]", document.getElementById('videos').files[x]);
        }
      var requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: formData,
          redirect: 'follow'
        };
      fetch("http://localhost:3000/api/v1/assignments/31/response_submit", requestOptions)
  .then(response => response.text())
  .then(result => console.log(result))
  .catch(error => console.log('error', error));
    })
  })
</script>
</body>
</html>