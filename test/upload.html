<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Testing File Uploads</title>
  <link href="https://unpkg.com/tailwindcss@^2/dist/tailwind.min.css" rel="stylesheet" />
</head>

<body>
  <section class="max-w-4xl mt-4 w-11/12 mx-auto bg-blue-50 p-8 shadow-lg rounded-lg">
    <h1 class="text-3xl font-semibold text-center">Add Bookings</h1>
    <form>
      <fieldset class="my-2">
        <label class="w-full block uppercase ml-3 mb-0" for="bookings_file">
          Bookings excel
        </label>
        <input type="file" name="bookings_file" id="bookings_file" class="w-full block p-3"
          placeholder="Bookings excel" />
      </fieldset>
      <button class="w-full block p-3 bg-green-400 hover:bg-green-500 text-center transition duration-200"
        type="submit">
        Import Bookings
      </button>
    </form>
  </section>
  <script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
      document.addEventListener('submit', (e) => {
        e.preventDefault()
        const form = e.target
        var myHeaders = new Headers();
        myHeaders.append("access-token", "6OclhVwqvRTNVXdBxFplcA");
        myHeaders.append("token-type", "Bearer");
        myHeaders.append("client", "AtmaTSoQdbNIRrJxUaqlhA");
        myHeaders.append("uid", "<EMAIL>");
        const formData = new FormData()
        formData.append('bookings_file', form.bookings_file.files[0], form.bookings_file.value)
        var requestOptions = {
          method: 'POST',
          headers: myHeaders,
          body: formData,
          redirect: 'follow'
        };
        fetch("http://localhost:3000/api/v1/bookings/import", requestOptions)
          .then(response => response.text())
          .then(result => console.log(result))
          .catch(error => console.log('error', error));
      })
    })
  </script>
</body>

</html>
